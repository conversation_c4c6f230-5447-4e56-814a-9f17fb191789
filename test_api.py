#!/usr/bin/env python3
"""
Simple test script for the FlexTerm Q&A API
"""

import requests
import json

def test_health():
    """Test the health endpoint"""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get("http://localhost:8000/api/v1/health")
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_ask(question):
    """Test the ask endpoint"""
    print(f"\n🤖 Testing Q&A with question: '{question}'")
    try:
        payload = {"question": question}
        response = requests.post(
            "http://localhost:8000/api/v1/ask",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Success!")
            print(f"📄 Full Response: {json.dumps(result, indent=2)}")
            if 'verbose_answer' in result:
                print(f"📝 Verbose Answer: {result['verbose_answer'][:200]}...")
                print(f"📋 Concise Answer: {result['concise_answer']}")
                print(f"📚 Sources: {len(result['sources'])} documents")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Q&A test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 FlexTerm Q&A API Test Suite")
    print("=" * 50)
    
    # Test health
    if not test_health():
        print("❌ Health check failed, stopping tests")
        return
    
    # Test Q&A with different questions
    questions = [
        "What is FlexTerm?",
        "How do I configure crane sequences?",
        "What are the main features of FlexTerm?"
    ]
    
    for question in questions:
        test_ask(question)
        print("-" * 50)
    
    print("✅ All tests completed!")

if __name__ == "__main__":
    main()
