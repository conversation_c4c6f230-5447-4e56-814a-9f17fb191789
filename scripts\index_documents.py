"""<PERSON><PERSON><PERSON> to index FlexTerm documents into the vector database."""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import app modules
sys.path.append(str(Path(__file__).parent.parent))

from app.core.config import settings
from app.services.document_processor import get_document_processor
from app.services.vector_store import get_vector_store

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def index_documents():
    """Index all documents in the document directory."""
    try:
        logger.info("Starting document indexing process...")
        
        # Initialize services
        document_processor = get_document_processor()
        vector_store = get_vector_store()
        
        # Check if vector store is ready
        if not vector_store.is_ready():
            logger.error("Vector store is not ready")
            return False
        
        # Check if documents directory exists
        if not os.path.exists(settings.doc_directory):
            logger.error(f"Document directory does not exist: {settings.doc_directory}")
            return False
        
        # Get current document count
        current_count = vector_store.get_document_count()
        logger.info(f"Current documents in vector store: {current_count}")
        
        # Ask user if they want to reindex (clear existing documents)
        if current_count > 0:
            response = input(f"Vector store contains {current_count} documents. Reindex (clear and rebuild)? [y/N]: ")
            if response.lower() in ['y', 'yes']:
                logger.info("Clearing existing documents...")
                vector_store.delete_collection()
        
        # Process all documents in the directory
        logger.info(f"Processing documents from: {settings.doc_directory}")
        all_chunks = document_processor.process_directory(settings.doc_directory)
        
        if not all_chunks:
            logger.warning("No documents found to index")
            return False
        
        logger.info(f"Found {len(all_chunks)} document chunks to index")
        
        # Prepare data for vector store
        texts = [chunk['text'] for chunk in all_chunks]
        metadatas = [chunk['metadata'] for chunk in all_chunks]
        ids = [chunk['id'] for chunk in all_chunks]
        
        # Add documents to vector store in batches
        batch_size = 50  # Process in smaller batches to avoid memory issues
        total_batches = (len(texts) + batch_size - 1) // batch_size
        
        for i in range(0, len(texts), batch_size):
            batch_num = (i // batch_size) + 1
            end_idx = min(i + batch_size, len(texts))
            
            logger.info(f"Processing batch {batch_num}/{total_batches} ({end_idx - i} documents)")
            
            batch_texts = texts[i:end_idx]
            batch_metadatas = metadatas[i:end_idx]
            batch_ids = ids[i:end_idx]
            
            success = await vector_store.add_documents(batch_texts, batch_metadatas, batch_ids)
            
            if not success:
                logger.error(f"Failed to index batch {batch_num}")
                return False
        
        # Verify indexing
        final_count = vector_store.get_document_count()
        logger.info(f"Indexing completed! Total documents in vector store: {final_count}")
        
        return True
        
    except Exception as e:
        logger.error(f"Document indexing failed: {e}")
        return False


async def test_search():
    """Test the search functionality with a sample query."""
    try:
        logger.info("Testing search functionality...")
        
        vector_store = get_vector_store()
        
        # Test query
        test_query = "How to configure a quay crane in FlexTerm?"
        
        documents, metadatas, scores = await vector_store.search(test_query, top_k=3)
        
        if documents:
            logger.info(f"Search test successful! Found {len(documents)} relevant documents")
            for i, (doc, metadata, score) in enumerate(zip(documents, metadatas, scores)):
                logger.info(f"Result {i+1} (score: {score:.3f}): {metadata.get('source_file', 'Unknown')} - {doc[:100]}...")
        else:
            logger.warning("Search test returned no results")
            
    except Exception as e:
        logger.error(f"Search test failed: {e}")


def main():
    """Main function to run the indexing process."""
    print("FlexTerm Document Indexing Script")
    print("=" * 40)
    
    # Run indexing
    success = asyncio.run(index_documents())
    
    if success:
        print("\n✅ Document indexing completed successfully!")
        
        # Run search test
        print("\nRunning search test...")
        asyncio.run(test_search())
        
        print("\n🎉 Ready to start the Q&A service!")
    else:
        print("\n❌ Document indexing failed. Check the logs for details.")
        sys.exit(1)


if __name__ == "__main__":
    main()
