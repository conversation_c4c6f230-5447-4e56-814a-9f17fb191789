"""Main Q&A service orchestrating RAG pipeline."""

import logging
from typing import <PERSON>ple

from app.core.llm import get_llm_service
from app.services.retrieval import get_retrieval_service
from app.models.schemas import QuestionResponse, ResponseData, RetrievalInfo, SourceInfo

logger = logging.getLogger(__name__)


class QAService:
    """Main Q&A service orchestrating the RAG pipeline."""
    
    def __init__(self):
        self.llm_service = get_llm_service()
        self.retrieval_service = get_retrieval_service()
    
    async def answer_question(self, question: str) -> QuestionResponse:
        """
        Answer a question using the RAG pipeline.
        
        Args:
            question: User question about FlexTerm
            
        Returns:
            QuestionResponse with formatted answer and metadata
        """
        try:
            logger.info(f"Processing question: {question[:100]}...")
            
            # Step 1: Retrieve relevant context
            context, sources, retrieval_info = await self.retrieval_service.retrieve_context(question)
            
            # Step 2: Generate response using LLM with context
            verbose_response, concise_response = await self.llm_service.generate_response(
                question=question,
                context=context
            )
            
            # Step 3: Format response
            response_data = ResponseData(
                verbose=verbose_response,
                concise=concise_response
            )
            
            # Step 4: Create final response
            qa_response = QuestionResponse(
                status="success",
                response=response_data,
                sources=sources,
                retrieval_info=retrieval_info
            )
            
            logger.info("Successfully generated Q&A response")
            return qa_response
            
        except Exception as e:
            logger.error(f"Failed to answer question: {e}")
            return self._create_error_response(str(e))
    
    def _create_error_response(self, error_message: str) -> QuestionResponse:
        """Create error response in the expected format."""
        verbose_response = f"""### Verbose Response
[AI Response]
I apologize, but I encountered an error while processing your FlexTerm question. The error was: {error_message}

Please try rephrasing your question or contact www.flexterm.com for assistance with FlexTerm-related inquiries."""
        
        concise_response = """### Concise Response
- Error occurred while processing your FlexTerm question
- Please try rephrasing your question
- Contact www.flexterm.com for technical support"""
        
        return QuestionResponse(
            status="error",
            response=ResponseData(
                verbose=verbose_response,
                concise=concise_response
            ),
            sources=[],
            retrieval_info=RetrievalInfo(
                chunks_retrieved=0,
                relevance_scores=[],
                embedding_source="error"
            )
        )
    
    def is_ready(self) -> bool:
        """Check if the Q&A service is ready to handle requests."""
        return (
            self.llm_service.is_available() and
            self.retrieval_service.vector_store.is_ready()
        )


# Global Q&A service instance
qa_service = QAService()


def get_qa_service() -> QAService:
    """Get the global Q&A service instance."""
    return qa_service
