"""Main FastAPI application for FlexTerm Q&A service."""

import logging
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager

from app.api.routes import router
from app.core.config import settings
from app.models.schemas import ErrorResponse
from app import __version__

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting FlexTerm Q&A Service...")
    logger.info(f"Version: {__version__}")
    logger.info(f"OpenAI Model: {settings.openai_model}")
    logger.info(f"Embedding Model: {settings.embedding_model}")
    logger.info(f"Vector DB Path: {settings.chroma_db_path}")
    
    # Check if services are ready
    from app.services.qa_service import get_qa_service
    qa_service = get_qa_service()
    
    if qa_service.is_ready():
        logger.info("✅ Q&A service is ready!")
    else:
        logger.warning("⚠️  Q&A service is not fully ready. Some components may need initialization.")
        logger.warning("💡 Run 'python scripts/index_documents.py' to index documents if needed.")
    
    yield
    
    # Shutdown
    logger.info("Shutting down FlexTerm Q&A Service...")


# Create FastAPI application
app = FastAPI(
    title="FlexTerm Q&A Service",
    description="""
    **FlexTerm Q&A Service with RAG Architecture**
    
    This service provides intelligent question-answering capabilities for FlexTerm simulation and emulation platform.
    
    ## Features
    - **RAG (Retrieval-Augmented Generation)**: Combines document retrieval with LLM generation
    - **Local Embeddings**: Uses Ollama with nomic-embed-text model (with OpenAI fallback)
    - **Dual Response Format**: Provides both verbose and concise answers
    - **Multi-language Support**: Supports both English and Chinese queries
    - **Source Attribution**: Shows which documents were used to generate answers
    
    ## Usage
    1. **POST /ask**: Submit a question about FlexTerm
    2. **GET /health**: Check service health and component status
    3. **GET /stats**: View knowledge base statistics
    
    ## Response Format
    All answers include:
    - **Verbose Response**: Detailed, professional explanation
    - **Concise Response**: Brief bullet points (max 5 points)
    - **Source Information**: Referenced documents with page/section details
    - **Retrieval Metadata**: Information about the search process
    """,
    version=__version__,
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router, prefix="/api/v1", tags=["Q&A"])

# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            status="error",
            message=exc.detail,
            detail=f"HTTP {exc.status_code}"
        ).dict()
    )


@app.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            status="error",
            message="Internal server error",
            detail=str(exc)
        ).dict()
    )

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with service information."""
    return {
        "service": "FlexTerm Q&A Service",
        "version": __version__,
        "description": "RAG-powered question answering for FlexTerm simulation platform",
        "docs": "/docs",
        "health": "/api/v1/health",
        "ask": "/api/v1/ask"
    }


def main():
    """Main function to run the application."""
    logger.info(f"Starting server on {settings.api_host}:{settings.api_port}")
    
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=True,  # Set to False in production
        log_level=settings.log_level.lower()
    )


if __name__ == "__main__":
    main()
