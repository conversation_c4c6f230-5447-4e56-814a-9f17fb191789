"""Vector store service using Chroma database."""

import logging
import os
from typing import List, Dict, Any, Optional, Tuple
import chromadb
from chromadb.config import Settings as ChromaSettings
from chromadb.utils import embedding_functions

from app.core.config import settings
from app.core.embeddings import get_embedding_service

logger = logging.getLogger(__name__)


class VectorStore:
    """Vector store service using Chroma database."""
    
    def __init__(self):
        self.client = None
        self.collection = None
        self.embedding_service = get_embedding_service()
        self._initialize_chroma()
    
    def _initialize_chroma(self):
        """Initialize Chroma database."""
        try:
            # Create data directory if it doesn't exist
            os.makedirs(settings.chroma_db_path, exist_ok=True)
            
            # Initialize Chroma client with persistent storage
            self.client = chromadb.PersistentClient(
                path=settings.chroma_db_path,
                settings=ChromaSettings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Get or create collection
            self.collection = self.client.get_or_create_collection(
                name=settings.collection_name,
                metadata={"description": "FlexTerm documentation embeddings"}
            )
            
            logger.info(f"Chroma vector store initialized at: {settings.chroma_db_path}")
            logger.info(f"Collection '{settings.collection_name}' ready with {self.collection.count()} documents")
            
        except Exception as e:
            logger.error(f"Failed to initialize Chroma vector store: {e}")
            raise
    
    async def add_documents(
        self, 
        texts: List[str], 
        metadatas: List[Dict[str, Any]], 
        ids: List[str]
    ) -> bool:
        """
        Add documents to the vector store.
        
        Args:
            texts: List of text chunks
            metadatas: List of metadata dictionaries
            ids: List of unique document IDs
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not texts or len(texts) != len(metadatas) or len(texts) != len(ids):
                raise ValueError("texts, metadatas, and ids must have the same length")
            
            # Generate embeddings
            embeddings, embedding_source = await self.embedding_service.generate_embeddings(texts)
            
            if not embeddings:
                raise Exception("Failed to generate embeddings")
            
            # Add embedding source to metadata
            for metadata in metadatas:
                metadata['embedding_source'] = embedding_source
            
            # Add to Chroma collection
            self.collection.add(
                embeddings=embeddings,
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(f"Added {len(texts)} documents to vector store using {embedding_source} embeddings")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add documents to vector store: {e}")
            return False
    
    async def search(
        self, 
        query: str, 
        top_k: Optional[int] = None
    ) -> Tuple[List[str], List[Dict[str, Any]], List[float]]:
        """
        Search for similar documents.
        
        Args:
            query: Search query text
            top_k: Number of results to return (defaults to settings.retrieval_top_k)
            
        Returns:
            Tuple of (documents, metadatas, distances)
        """
        try:
            if top_k is None:
                top_k = settings.retrieval_top_k
            
            # Generate query embedding
            query_embedding, embedding_source = await self.embedding_service.generate_single_embedding(query)
            
            if not query_embedding:
                raise Exception("Failed to generate query embedding")
            
            # Search in Chroma
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                include=['documents', 'metadatas', 'distances']
            )
            
            # Extract results
            documents = results['documents'][0] if results['documents'] else []
            metadatas = results['metadatas'][0] if results['metadatas'] else []
            distances = results['distances'][0] if results['distances'] else []
            
            # Convert distances to similarity scores (1 - distance)
            similarity_scores = [1 - dist for dist in distances]
            
            logger.info(f"Retrieved {len(documents)} documents using {embedding_source} embeddings")
            
            return documents, metadatas, similarity_scores
            
        except Exception as e:
            logger.error(f"Failed to search vector store: {e}")
            return [], [], []
    
    def get_document_count(self) -> int:
        """Get the number of documents in the vector store."""
        try:
            return self.collection.count()
        except Exception as e:
            logger.error(f"Failed to get document count: {e}")
            return 0
    
    def delete_collection(self) -> bool:
        """Delete the entire collection (for reindexing)."""
        try:
            self.client.delete_collection(settings.collection_name)
            logger.info(f"Deleted collection: {settings.collection_name}")
            
            # Recreate collection
            self.collection = self.client.get_or_create_collection(
                name=settings.collection_name,
                metadata={"description": "FlexTerm documentation embeddings"}
            )
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete collection: {e}")
            return False
    
    def is_ready(self) -> bool:
        """Check if vector store is ready."""
        return self.client is not None and self.collection is not None


# Global vector store instance
vector_store = VectorStore()


def get_vector_store() -> VectorStore:
    """Get the global vector store instance."""
    return vector_store
