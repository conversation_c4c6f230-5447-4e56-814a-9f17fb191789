"""Configuration management for FlexTerm Q&A Agent."""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # OpenAI Configuration
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4o", env="OPENAI_MODEL")
    max_tokens: int = Field(default=2000, env="MAX_TOKENS")
    
    # Ollama Configuration
    ollama_base_url: str = Field(default="http://localhost:11434", env="OLLAMA_BASE_URL")
    embedding_model: str = Field(default="nomic-embed-text:latest", env="EMBEDDING_MODEL")
    
    # Vector Database Configuration
    chroma_db_path: str = Field(default="./data/chroma_db", env="CHROMA_DB_PATH")
    collection_name: str = Field(default="flexterm_docs", env="COLLECTION_NAME")
    
    # RAG Configuration
    retrieval_top_k: int = Field(default=7, env="RETRIEVAL_TOP_K")
    chunk_size: int = Field(default=1024, env="CHUNK_SIZE")
    chunk_overlap: int = Field(default=100, env="CHUNK_OVERLAP")
    
    # Application Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    
    # Document Processing
    doc_directory: str = Field(default="./doc", env="DOC_DIRECTORY")
    prompt_file: str = Field(default="./prompt.md", env="PROMPT_FILE")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings
