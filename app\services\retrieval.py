"""Retrieval service for RAG-based question answering."""

import logging
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass

from app.core.config import settings
from app.services.vector_store import get_vector_store
from app.models.schemas import SourceInfo, RetrievalInfo

logger = logging.getLogger(__name__)


@dataclass
class RetrievedDocument:
    """Container for retrieved document with metadata."""
    text: str
    metadata: Dict[str, Any]
    relevance_score: float
    source_info: SourceInfo


class RetrievalService:
    """Service for retrieving relevant documents for RAG."""
    
    def __init__(self):
        self.vector_store = get_vector_store()
        self.min_relevance_threshold = 0.3  # Minimum relevance score to include
    
    async def retrieve_context(
        self, 
        query: str, 
        top_k: Optional[int] = None
    ) -> Tuple[str, List[SourceInfo], RetrievalInfo]:
        """
        Retrieve relevant context for a query.
        
        Args:
            query: User question
            top_k: Number of documents to retrieve
            
        Returns:
            Tuple of (formatted_context, source_info_list, retrieval_info)
        """
        try:
            if top_k is None:
                top_k = settings.retrieval_top_k
            
            # Search vector store
            documents, metadatas, scores = await self.vector_store.search(query, top_k)
            
            if not documents:
                logger.warning(f"No documents retrieved for query: {query[:50]}...")
                return "", [], RetrievalInfo(
                    chunks_retrieved=0,
                    relevance_scores=[],
                    embedding_source="none"
                )
            
            # Filter by relevance threshold
            filtered_docs = self._filter_by_relevance(documents, metadatas, scores)
            
            if not filtered_docs:
                logger.warning(f"No documents above relevance threshold for query: {query[:50]}...")
                return "", [], RetrievalInfo(
                    chunks_retrieved=0,
                    relevance_scores=[],
                    embedding_source="filtered_out"
                )
            
            # Deduplicate and rank documents
            ranked_docs = self._rank_and_deduplicate(filtered_docs)
            
            # Format context
            formatted_context = self._format_context(ranked_docs)
            
            # Extract source information
            source_info = self._extract_source_info(ranked_docs)
            
            # Create retrieval info
            retrieval_info = RetrievalInfo(
                chunks_retrieved=len(ranked_docs),
                relevance_scores=[doc.relevance_score for doc in ranked_docs],
                embedding_source=metadatas[0].get('embedding_source', 'unknown') if metadatas else 'unknown'
            )
            
            logger.info(f"Retrieved {len(ranked_docs)} relevant documents for query")
            
            return formatted_context, source_info, retrieval_info
            
        except Exception as e:
            logger.error(f"Failed to retrieve context: {e}")
            return "", [], RetrievalInfo(
                chunks_retrieved=0,
                relevance_scores=[],
                embedding_source="error"
            )
    
    def _filter_by_relevance(
        self, 
        documents: List[str], 
        metadatas: List[Dict[str, Any]], 
        scores: List[float]
    ) -> List[RetrievedDocument]:
        """Filter documents by relevance threshold."""
        filtered_docs = []
        
        for doc, metadata, score in zip(documents, metadatas, scores):
            if score >= self.min_relevance_threshold:
                source_info = SourceInfo(
                    file=metadata.get('source_file', 'Unknown'),
                    section=metadata.get('section'),
                    page=metadata.get('page_number')
                )
                
                filtered_docs.append(RetrievedDocument(
                    text=doc,
                    metadata=metadata,
                    relevance_score=score,
                    source_info=source_info
                ))
        
        return filtered_docs
    
    def _rank_and_deduplicate(self, documents: List[RetrievedDocument]) -> List[RetrievedDocument]:
        """Rank documents and remove duplicates."""
        # Sort by relevance score (highest first)
        documents.sort(key=lambda x: x.relevance_score, reverse=True)
        
        # Simple deduplication based on text similarity
        deduplicated = []
        seen_texts = set()
        
        for doc in documents:
            # Use first 100 characters as a simple deduplication key
            text_key = doc.text[:100].strip().lower()
            
            if text_key not in seen_texts:
                deduplicated.append(doc)
                seen_texts.add(text_key)
        
        return deduplicated
    
    def _format_context(self, documents: List[RetrievedDocument]) -> str:
        """Format retrieved documents into context string."""
        if not documents:
            return ""
        
        context_parts = []
        
        for i, doc in enumerate(documents, 1):
            # Create source reference
            source_ref = f"[Source: {doc.source_info.file}"
            if doc.source_info.page:
                source_ref += f", Page {doc.source_info.page}"
            if doc.source_info.section:
                source_ref += f", Section: {doc.source_info.section}"
            source_ref += "]"
            
            # Format document chunk
            context_part = f"Document {i}:\n{source_ref}\n{doc.text}\n"
            context_parts.append(context_part)
        
        return "\n---\n".join(context_parts)
    
    def _extract_source_info(self, documents: List[RetrievedDocument]) -> List[SourceInfo]:
        """Extract unique source information from documents."""
        sources = {}
        
        for doc in documents:
            file_key = doc.source_info.file
            
            if file_key not in sources:
                sources[file_key] = doc.source_info
            else:
                # Merge information if from same file
                existing = sources[file_key]
                if doc.source_info.section and not existing.section:
                    existing.section = doc.source_info.section
                if doc.source_info.page and not existing.page:
                    existing.page = doc.source_info.page
        
        return list(sources.values())
    
    async def get_similar_questions(self, query: str, top_k: int = 3) -> List[str]:
        """Get similar questions that might help the user."""
        try:
            # This is a placeholder for future enhancement
            # Could implement question similarity search or FAQ matching
            return []
            
        except Exception as e:
            logger.error(f"Failed to get similar questions: {e}")
            return []


# Global retrieval service instance
retrieval_service = RetrievalService()


def get_retrieval_service() -> RetrievalService:
    """Get the global retrieval service instance."""
    return retrieval_service
