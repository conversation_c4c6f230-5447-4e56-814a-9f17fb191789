# FlexTerm Q&A Agent Implementation Plan (RAG Architecture)

## Project Overview
Create a Python-based question and answer agent that serves as a web API for FlexTerm-related queries. The agent will use **RAG (Retrieval-Augmented Generation)** architecture with local embedding models (Ollama) and vector database for efficient document retrieval, combined with OpenAI GPT-4o for response generation.

## Architecture Overview

### Core Components
1. **Web API Server** (FastAPI)
2. **Document Processing & Chunking Module** (PDF + Markdown parsing)
3. **Embedding Service** (Ollama nomic-embed-text)
4. **Vector Database** (Chroma - local vector store)
5. **Retrieval Module** (Semantic search & context selection)
6. **LLM Integration Module** (OpenAI GPT-4o)
7. **Response Formatting Module** (Dual format: Verbose + Concise)
8. **Configuration Management** (.env handling)

### RAG Workflow
1. **Indexing Phase** (One-time setup):
   - Parse documents → Chunk text → Generate embeddings → Store in vector DB
2. **Query Phase** (Per request):
   - User question → Generate query embedding → Retrieve relevant chunks → Augment prompt → LLM response

## Detailed Implementation Plan

### Phase 1: Project Setup & Dependencies
- [ ] Create virtual environment
- [ ] Install required packages:
  - `fastapi` - Web API framework
  - `uvicorn` - ASGI server
  - `openai` - OpenAI API client
  - `python-dotenv` - Environment variable management
  - `pdfplumber` - PDF text extraction (better than PyPDF2)
  - `python-multipart` - For form data handling
  - `pydantic` - Data validation
  - `chromadb` - Local vector database
  - `ollama` - Python client for Ollama
  - `langchain` - RAG framework and text splitters
  - `langchain-community` - Community integrations
  - `sentence-transformers` - Fallback embedding option
  - `numpy` - Numerical operations for embeddings
- [ ] Create project structure:
  ```
  flexterm-jira/
  ├── main.py              # FastAPI application entry point
  ├── app/
  │   ├── __init__.py
  │   ├── api/
  │   │   ├── __init__.py
  │   │   └── routes.py    # API endpoints
  │   ├── core/
  │   │   ├── __init__.py
  │   │   ├── config.py    # Configuration management
  │   │   ├── llm.py       # OpenAI integration
  │   │   └── embeddings.py # Ollama embedding service
  │   ├── services/
  │   │   ├── __init__.py
  │   │   ├── document_processor.py  # Document parsing & chunking
  │   │   ├── vector_store.py        # Chroma vector database
  │   │   ├── retrieval.py           # RAG retrieval logic
  │   │   └── qa_service.py          # Q&A orchestration
  │   └── models/
  │       ├── __init__.py
  │       └── schemas.py   # Pydantic models
  ├── data/
  │   └── chroma_db/       # Vector database storage
  ├── scripts/
  │   └── index_documents.py # One-time indexing script
  ├── requirements.txt
  ├── .env                 # Already exists
  ├── doc/                 # Already exists
  └── prompt.md           # Already exists
  ```

### Phase 2: RAG Infrastructure Setup
- [ ] **Vector Database Setup**
  - Initialize Chroma database in `/data/chroma_db/`
  - Configure collection for FlexTerm documents
  - Set up persistence and backup strategies

- [ ] **Embedding Service**
  - Primary: Integrate with Ollama `nomic-embed-text:latest` model
  - Fallback: OpenAI `text-embedding-3-small` when Ollama unavailable
  - Implement embedding generation for documents and queries
  - Handle embedding dimension consistency (768 vs 1536 dimensions)
  - Smart fallback detection and switching

### Phase 3: Document Processing & Indexing
- [ ] **PDF Text Extraction**
  - Extract text from `FXTMANUAL_documentation-with-image.pdf`
  - Handle potential OCR issues and formatting
  - Preserve document structure and metadata

- [ ] **Markdown Processing**
  - Parse all `.md` files in `/doc/` directory
  - Extract structured content (headers, sections, code blocks)
  - Maintain hierarchy and cross-references

- [ ] **Intelligent Text Chunking**
  - Implement semantic chunking using LangChain
  - Preserve context boundaries (sections, paragraphs)
  - **Chunk size: 1024 tokens** (optimized for nomic-embed-text)
  - Add 100-token overlap between chunks for context continuity

- [ ] **Document Indexing Pipeline**
  - Create embeddings for all text chunks
  - Store in Chroma with metadata (source file, section, page number)
  - Implement incremental indexing for updates
  - Create indexing script for initial setup

### Phase 4: RAG Retrieval System
- [ ] **Semantic Search Implementation**
  - Query embedding generation using Ollama (with OpenAI fallback)
  - Similarity search in Chroma vector database
  - **Retrieve 5-10 chunks per query** (configurable)
  - Relevance score filtering and threshold tuning

- [ ] **Context Selection & Ranking**
  - Implement re-ranking algorithms for retrieved chunks
  - Context deduplication and merging
  - Optimal context window management for GPT-4o
  - Source attribution tracking

- [ ] **Retrieval Optimization**
  - Query expansion and reformulation
  - Hybrid search (semantic + keyword) if needed
  - Caching for frequently asked questions

### Phase 5: LLM Integration & RAG Pipeline
- [ ] **OpenAI Configuration**
  - Load API key from `.env` file
  - Configure GPT-4o model parameters
  - Implement error handling and retry logic

- [ ] **RAG Prompt Engineering**
  - Load base system prompt from `prompt.md`
  - Dynamic context injection with retrieved documents
  - Context formatting for optimal LLM understanding
  - Handle both English and Chinese language requirements

- [ ] **Response Processing**
  - Parse LLM responses to extract Verbose and Concise sections
  - Validate response format compliance
  - Handle edge cases (incomplete responses, errors)
  - Source citation integration

### Phase 6: Web API Development
- [ ] **API Endpoints**
  - `POST /ask` - Main Q&A endpoint
  - `GET /health` - Health check endpoint
  - `GET /docs` - Auto-generated API documentation

- [ ] **Request/Response Models**
  - Question input validation
  - Structured response format (JSON with markdown content)
  - Error response handling

- [ ] **API Features**
  - Request logging
  - Response caching (optional)
  - Rate limiting (optional)

### Phase 7: Response Formatting
- [ ] **Dual Response Format Implementation**
  - Ensure "[AI Response]" prefix is added
  - Parse and format "Verbose Response" section
  - Parse and format "Concise Response" section
  - Validate bullet point formatting (plain dashes, max 5 points)

- [ ] **Language Detection & Handling**
  - Detect input language (Chinese/English)
  - Ensure response language matches input
  - Handle mixed-language scenarios

### Phase 8: Testing & Validation
- [ ] **Unit Tests**
  - Document processing and chunking functions
  - Embedding generation and vector operations
  - Retrieval system accuracy
  - LLM integration
  - Response formatting
  - API endpoints

- [ ] **RAG System Tests**
  - Embedding quality and consistency
  - Retrieval relevance and ranking
  - Context selection optimization
  - End-to-end RAG pipeline

- [ ] **Integration Tests**
  - Complete Q&A flow with RAG
  - Error handling scenarios
  - Language switching
  - Performance under load

- [ ] **Manual Testing**
  - Test with sample FlexTerm questions
  - Validate response format compliance
  - Test both English and Chinese queries
  - Verify source attribution accuracy

### Phase 9: Deployment Preparation
- [ ] **Configuration**
  - Environment-specific settings
  - Logging configuration
  - Performance optimization

- [ ] **Documentation**
  - API usage examples
  - Deployment instructions
  - Troubleshooting guide

## Technical Specifications

### API Endpoint Design
```
POST /ask
Content-Type: application/json

Request:
{
  "question": "How do I configure a quay crane in FlexTerm?"
}

Response:
{
  "status": "success",
  "response": {
    "verbose": "### Verbose Response\n[AI Response]\n...",
    "concise": "### Concise Response\n- ...\n- ..."
  },
  "sources": [
    {
      "file": "FXTMANUAL_documentation-with-image.pdf",
      "page": 42,
      "section": "Quay Crane Configuration"
    },
    {
      "file": "FlexTerm_Helper_API.md",
      "section": "CheHelper"
    }
  ],
  "retrieval_info": {
    "chunks_retrieved": 5,
    "relevance_scores": [0.89, 0.82, 0.78, 0.75, 0.71]
  }
}
```

### Environment Variables
- `OPENAI_API_KEY` - Already configured
- `OLLAMA_BASE_URL` - Ollama server URL (default: http://localhost:11434)
- `EMBEDDING_MODEL` - Embedding model name (default: nomic-embed-text:latest)
- `CHROMA_DB_PATH` - Vector database path (default: ./data/chroma_db)
- `LOG_LEVEL` - Optional logging level
- `MAX_TOKENS` - Optional token limit for responses
- `RETRIEVAL_TOP_K` - Number of chunks to retrieve (default: 7, range: 5-10)

## Recommendations & Considerations

### 1. RAG Architecture Benefits
- **Local Embeddings**: Using Ollama `nomic-embed-text` eliminates external API dependencies for embeddings
- **Cost Efficiency**: Only OpenAI API calls for final response generation, not for document processing
- **Privacy**: All document processing and embedding happens locally
- **Performance**: Vector search is much faster than processing entire documents each time

### 2. Vector Database Choice - Chroma ✅ **CONFIRMED**
- **Selected**: Chroma for lightweight, easy setup, excellent Python integration
- **Benefits**: Supports metadata filtering, persistent storage, good for local development
- **Storage**: Local directory `/data/chroma_db/` with automatic persistence

### 3. Embedding Strategy ✅ **CONFIRMED**
- **Primary**: Ollama `nomic-embed-text:latest` (768-dim, local, fast, good quality)
- **Fallback**: OpenAI `text-embedding-3-small` (1536-dim, when Ollama unavailable)
- **Implementation**: Detect Ollama availability, graceful fallback with dimension handling
- **Consideration**: Store embedding source metadata to ensure consistency

### 4. Chunking Strategy ✅ **CONFIRMED**
- **Implementation**: LangChain's RecursiveCharacterTextSplitter with semantic awareness
- **Chunk Size**: **1024 tokens** (optimal for nomic-embed-text and context richness)
- **Overlap**: **100 tokens** between chunks for context continuity
- **Structure Preservation**: Don't split mid-sentence or mid-section

### 5. Retrieval Optimization ✅ **CONFIRMED**
- **Top-K Selection**: **5-10 chunks per query** (configurable, start with 7)
- **Re-ranking**: Implement relevance scoring and threshold filtering
- **Context Window**: Ensure retrieved content fits within GPT-4o limits (~8K tokens)
- **Balance**: Optimize for context richness while maintaining response speed

### 6. Performance & Scalability
- **Caching**: Cache embeddings and frequent queries
- **Indexing**: One-time setup script for initial document processing
- **Updates**: Incremental indexing when documents change
- **Consideration**: Monitor Ollama performance under concurrent requests

## Success Criteria
1. ✅ RAG system successfully indexes all FlexTerm documentation
2. ✅ Semantic search retrieves relevant document chunks accurately
3. ✅ API processes FlexTerm-related questions with proper context
4. ✅ Responses follow exact format specified in `prompt.md`
5. ✅ Both English and Chinese language support working
6. ✅ Source attribution is accurate and helpful
7. ✅ Response quality meets professional standards with relevant context
8. ✅ System performs well with local Ollama embeddings
9. ✅ API is stable and handles errors gracefully

## Next Steps
After plan approval:
1. Set up RAG infrastructure (Chroma, Ollama integration)
2. Implement document processing and indexing pipeline
3. Build retrieval system with semantic search
4. Integrate LLM with RAG context injection
5. Add web API layer with enhanced response format
6. Comprehensive testing of RAG pipeline and API
7. Performance optimization and refinement

## Initial Setup Commands
```bash
# Install Ollama (if not already installed)
# Ensure nomic-embed-text model is available
ollama pull nomic-embed-text:latest

# Create virtual environment
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows

# Install dependencies
pip install fastapi uvicorn openai python-dotenv pdfplumber chromadb ollama langchain langchain-community sentence-transformers numpy
```

---

**Estimated Development Time**: 3-4 days for RAG implementation + core functionality, additional time for optimization and testing.

**Key Dependencies**:
- OpenAI API access (for GPT-4o responses)
- Ollama running locally with nomic-embed-text model
- Python 3.8+
- Sufficient disk space for vector database (~100MB-1GB depending on document size)
