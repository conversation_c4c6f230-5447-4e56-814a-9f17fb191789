# FlexTerm Q&A System Migration Plan

## Overview
This document provides step-by-step instructions for migrating the FlexTerm Q&A system from one Windows 11 computer to another.

## Prerequisites on Target Machine

### 1. Python Installation
- Install Python 3.9+ from [python.org](https://python.org)
- Ensure Python is added to PATH during installation
- Verify installation: `python --version`

### 2. Git (Optional but Recommended)
- Install Git from [git-scm.com](https://git-scm.com)
- For version control and easy updates

## Migration Steps

### Step 1: Copy Project Files
Choose one of these methods:

#### Method A: Direct File Copy
1. Copy the entire project folder `flexterm-jira` to the target machine
2. Maintain the same directory structure
3. Ensure all files are copied including hidden files (`.env`, `.gitignore`)

#### Method B: Git Repository (Recommended)
1. Initialize git repository on source machine:
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   ```
2. Push to a remote repository (GitHub, GitLab, etc.)
3. <PERSON>lone on target machine:
   ```bash
   git clone <repository-url>
   ```

### Step 2: Environment Setup on Target Machine

#### 1. Navigate to Project Directory
```bash
cd flexterm-jira
```

#### 2. Create Virtual Environment
```bash
python -m venv .venv
```

#### 3. Activate Virtual Environment
```bash
# Windows
.venv\Scripts\activate
```

#### 4. Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 3: Configuration

#### 1. Environment Variables
- Copy `.env` file from source machine
- Update `OPENAI_API_KEY` if needed
- Verify other settings:
  ```
  OPENAI_API_KEY=your_openai_api_key_here
  OLLAMA_BASE_URL=http://localhost:11434
  EMBEDDING_MODEL=nomic-embed-text:latest
  CHROMA_DB_PATH=./data/chroma_db
  RETRIEVAL_TOP_K=7
  ```

#### 2. Document Directory
- Ensure `./doc` directory contains all FlexTerm documentation files:
  - `flexterm-lua-api-doc-local-no-example.md`
  - `FlexTerm_Helper_API.md`
  - `FXTMANUAL_documentation-with-image.pdf`
  - `QtScriptBinding-help.md`

### Step 4: Vector Database Migration

#### Option A: Copy Existing Database (Fastest)
1. Copy the entire `./data/chroma_db` directory from source machine
2. This preserves all indexed documents and embeddings
3. Skip to Step 5 if this method is used

#### Option B: Re-index Documents (Clean Start)
1. Run the indexing script:
   ```bash
   python scripts/index_documents.py
   ```
2. Wait for completion (may take 10-15 minutes depending on document size)
3. Verify successful indexing in the output

### Step 5: Testing and Verification

#### 1. Start the Service
```bash
python main.py
```

#### 2. Verify Health Check
```bash
# In another terminal
python test_api.py
```
Or visit: http://localhost:8000/api/v1/health

#### 3. Test Q&A Functionality
- **Web Frontend**: Visit http://localhost:8000 for the main user interface
- **Test Script**: Use `python test_api.py` for automated testing
- **API Docs**: Visit http://localhost:8000/docs for interactive API testing
- Try sample questions like "What is FlexTerm?"

## Troubleshooting

### Common Issues and Solutions

#### 1. Python Version Conflicts
- Ensure Python 3.9+ is installed
- Use `python --version` to verify
- Consider using `py -3.9` if multiple Python versions exist

#### 2. Virtual Environment Issues
```bash
# If activation fails, try:
python -m venv --clear .venv
.venv\Scripts\activate
```

#### 3. Package Installation Errors
```bash
# Update pip first
python -m pip install --upgrade pip
pip install -r requirements.txt
```

#### 4. OpenAI API Issues
- Verify API key is correct in `.env`
- Check API key permissions and billing status
- Test with a simple API call

#### 5. Port Already in Use
- Change port in `main.py` if 8000 is occupied:
  ```python
  uvicorn.run(app, host="0.0.0.0", port=8001)  # Use different port
  ```

#### 6. Vector Database Issues
- Delete `./data/chroma_db` and re-run indexing
- Check document files are in `./doc` directory
- Verify file permissions

## Optional Enhancements

### 1. Ollama Installation (Local Embeddings)
1. Download Ollama from [ollama.ai](https://ollama.ai)
2. Install and start Ollama service
3. Pull the embedding model:
   ```bash
   ollama pull nomic-embed-text
   ```
4. Restart the Q&A service to use local embeddings

### 2. Windows Service Setup
- Consider setting up the Q&A service as a Windows service for automatic startup
- Use tools like NSSM (Non-Sucking Service Manager)

### 3. Firewall Configuration
- If accessing from other machines, configure Windows Firewall
- Allow inbound connections on port 8000

## File Checklist

Ensure these files/directories are migrated:

### Essential Files
- [ ] `main.py` - Main application entry point
- [ ] `requirements.txt` - Python dependencies
- [ ] `.env` - Environment configuration
- [ ] `app/` - Application source code directory
- [ ] `scripts/` - Utility scripts
- [ ] `doc/` - FlexTerm documentation files
- [ ] `static/` - Web frontend files

### Optional Files
- [ ] `test_api.py` - API testing script
- [ ] `README.md` - Project documentation
- [ ] `data/chroma_db/` - Pre-built vector database (if copying)
- [ ] `.gitignore` - Git ignore rules

## Post-Migration Verification

### 1. Service Health
- [ ] Health endpoint returns 200 OK
- [ ] Vector database shows as ready
- [ ] OpenAI API connectivity confirmed

### 2. Functionality Tests
- [ ] Document retrieval working
- [ ] Q&A responses generated correctly
- [ ] Both verbose and concise formats working
- [ ] Source attribution included

### 3. Performance Check
- [ ] Response times acceptable (< 10 seconds)
- [ ] Memory usage reasonable
- [ ] No error messages in logs

## Support and Maintenance

### Regular Tasks
- Monitor API usage and costs
- Update dependencies periodically
- Backup vector database if significant changes made
- Update documentation as FlexTerm evolves

### Getting Help
- Check logs in terminal output
- Review error messages carefully
- Consult OpenAI API documentation for API-related issues
- Contact FlexTerm support for domain-specific questions

---

**Migration Complete!** 🎉

Your FlexTerm Q&A system should now be fully operational on the new Windows 11 machine.
