# Qt Script Bindings Usage Guide

This document provides a comprehensive guide to all Qt Script bindings available in the QtScriptBinder, organized by functionality for easy navigation.

## Table of Contents

1. [Usage Rules](#usage-rules)
2. [Core Functions](#core-functions)
3. [Dialog & Message Functions](#dialog--message-functions)
4. [Basic Widgets](#basic-widgets)
5. [Input Widgets](#input-widgets)
6. [Display Widgets](#display-widgets)
7. [Container Widgets](#container-widgets)
8. [Layout Management](#layout-management)
9. [Window & Application](#window--application)
10. [Events & Testing](#events--testing)
11. [Data Types](#data-types)
12. [Constants](#constants)
13. [Complete Examples](#complete-examples)

---

## Usage Rules

- **Global Functions**: Direct function call - `functionName()`
- **Static Functions**: Use `.` (dot notation) - `ClassName.functionName()`  
- **Instance Methods**: Use `:` (colon notation) - `object:methodName()`
- **Properties**: Use `.` (dot notation) - `object.property = value`

---

## Core Functions

### UI Loading & Object Management
```lua
-- Global Functions
loadUi("path/to/ui/file.ui")                        -- Load UI from file
connect(object, "signal()", functionCallback)       -- Connect signal to Lua function  
findChild(parentObject, "childName")                -- Find child object by name

-- Static Functions  
QObject.findChild(parentObject, "childName")        -- Find child object (static)
QObject.getObjectName(object)                       -- Get object name as string
QObject.tr("Text to translate")                     -- Translate text

-- Instance Methods
object:setParent(parentObject)                      -- Set parent object
object:connect("signal()", function)                -- Connect signal to function
object:disconnect()                                  -- Disconnect all signals
```

---

## Dialog & Message Functions

### Message & Input Dialogs
```lua
-- Global Functions
popMsg("Hello World")                                -- Show popup message
inputDialog("Title", "Label", "Default Text")       -- Show input dialog
openFileDialog("Title", "Filter", "Default Path")   -- Show file open dialog
saveFolderDialog("Title", "Filter", "Default Path") -- Show file save dialog  
openFolderDialog("Title", "Default Path")           -- Show folder dialog

-- QMessageBox Methods
messageBox:addButton(QMessageBox.Ok)                -- Add standard button
messageBox:setDefaultButton(QMessageBox.Ok)         -- Set default button
messageBox:setText("Message text")                  -- Set message text

-- QDialog Methods
dialog:exec()                                        -- Execute dialog modally
dialog:accept()                                      -- Accept dialog
dialog:reject()                                      -- Reject dialog
dialog:done(result)                                  -- Close with result code
```

---

## Basic Widgets

### QWidget (Base Widget)
```lua
-- Instance Methods
widget:show()                                        -- Show widget
widget:close()                                       -- Close widget
widget:hide()                                        -- Hide widget
widget:showMaximized()                               -- Show maximized
widget:resize(800, 600)                              -- Resize widget
widget:update()                                      -- Update/repaint widget
widget:setWindowTitle("Window Title")               -- Set window title
widget:setParent(parentWidget)                       -- Set parent widget
widget:setStyleSheet("color: red;")                  -- Set CSS-like styles
widget:addAction(action)                             -- Add action to widget
widget:childAt(10, 20)                               -- Get child widget at position

-- Properties
widget.visible = true                                -- Set/get visibility
widget.enabled = false                               -- Set/get enabled state
```

### QPushButton
```lua
-- Instance Methods
button:setText("Click Me")                           -- Set button text
button:setIcon(icon)                                 -- Set button icon
button:setIconSize(size)                             -- Set icon size

-- Properties
button.text = "Click Me"                             -- Set/get button text
button.menu = contextMenu                            -- Set/get button menu
```

### QLabel
```lua
-- Properties
label.text = "Hello World"                           -- Set/get label text
```

### QCheckBox & QRadioButton
```lua
-- Instance Methods
checkBox:isChecked()                                 -- Check if checked
checkBox:setChecked(true)                            -- Set checked state
radioButton:setAutoExclusive(true)                   -- Set auto exclusive (radio only)
radioButton:toggled()                                -- Get toggled signal (radio only)
```

---

## Input Widgets

### QLineEdit (Text Input)
```lua
-- Properties
lineEdit.text = "Input text"                         -- Set/get text content
lineEdit.placeholderText = "Enter text..."           -- Set/get placeholder text
```

### QComboBox (Dropdown)
```lua
-- Instance Methods
comboBox:addItem("Item Name")                        -- Add item to dropdown
comboBox:setModel(model)                             -- Set data model
comboBox:setInsertPolicy(policy)                     -- Set insert policy
comboBox:clear()                                     -- Clear all items

-- Properties
comboBox.currentText = "Selected Item"               -- Set/get current text
comboBox.currentIndex = 2                            -- Set/get current index
comboBox.editable = true                             -- Set/get editable state
```

### QSpinBox (Number Input)
```lua
-- Instance Methods
spinBox:setRange(0, 100)                             -- Set min/max range

-- Properties
spinBox.value = 50                                   -- Set/get spinner value
spinBox.minimum = 0                                  -- Set/get minimum value
spinBox.maximum = 100                                -- Set/get maximum value
spinBox.suffix = " px"                               -- Set/get suffix text
spinBox.prefix = "Width: "                           -- Set/get prefix text
```

### QSlider
```lua
-- Instance Methods
slider:value()                                       -- Get slider value
slider:setValue(50)                                  -- Set slider value
slider:setOrientation(orientation)                   -- Set horizontal/vertical
slider:setRange(0, 100)                              -- Set value range
```

### Date & Time Widgets
```lua
-- QDateEdit Properties
dateEdit.date = QDate()                              -- Set/get date

-- QTimeEdit Properties  
timeEdit.time = QTime()                              -- Set/get time

-- QDateTimeEdit Properties
dateTimeEdit.dateTime = QDateTime()                  -- Set/get date and time

-- QDateTimeEdit Methods
dateTimeEdit:setDisplayFormat("yyyy-MM-dd")         -- Set display format
dateTimeEdit:setTime(time)                           -- Set time component
dateEdit:setCalendarPopup(true)                     -- Enable calendar popup
```

---

## Display Widgets

### QTextEdit & QPlainTextEdit
```lua
-- Instance Methods
textEdit:setPlainText("Hello World")                -- Set plain text content
textEdit:toPlainText()                               -- Get plain text content
plainTextEdit:setPlainText("Hello World")           -- Set plain text content  
plainTextEdit:toPlainText()                          -- Get plain text content
```

### QListWidget (List)
```lua
-- Instance Methods
listWidget:addItem("Item Text")                      -- Add item to list
listWidget:insertItem(0, "Item Text")               -- Insert item at index
listWidget:item(0)                                   -- Get item at index
listWidget:takeItem(0)                               -- Remove and return item
listWidget:currentItem()                             -- Get currently selected item
listWidget:setCurrentRow(0)                         -- Set current selection
listWidget:currentRow()                              -- Get current row index
listWidget:count()                                   -- Get total item count
listWidget:clear()                                   -- Clear all items
listWidget:editItem(item)                            -- Edit item in-place
listWidget:itemWidget(item)                          -- Get custom widget for item
listWidget:setItemWidget(item, widget)              -- Set custom widget for item
listWidget:removeItemWidget(item)                    -- Remove custom widget
```

### QTableWidget (Table)
```lua
-- Instance Methods
tableWidget:setRowCount(10)                          -- Set number of rows
tableWidget:setColumnCount(5)                        -- Set number of columns
tableWidget:setItem(0, 0, item)                      -- Set item at row,col
tableWidget:item(0, 0)                               -- Get item at row,col
tableWidget:takeItem(0, 0)                           -- Remove and return item
tableWidget:currentRow()                             -- Get current row
tableWidget:currentColumn()                          -- Get current column
tableWidget:setCurrentCell(0, 0)                     -- Set current cell
tableWidget:currentItem()                            -- Get current item
tableWidget:setCurrentItem(item)                     -- Set current item
tableWidget:rowCount()                               -- Get row count
tableWidget:columnCount()                            -- Get column count
tableWidget:removeRow(0)                             -- Remove row
tableWidget:removeColumn(0)                          -- Remove column
tableWidget:clear()                                  -- Clear all items
tableWidget:resizeColumnsToContents()               -- Auto-resize columns
tableWidget:resizeRowsToContents()                  -- Auto-resize rows
tableWidget:setHorizontalHeaderLabels({"Col1", "Col2"}) -- Set column headers
tableWidget:setVerticalHeaderLabels({"Row1", "Row2"}) -- Set row headers
tableWidget:cellWidget(0, 0)                        -- Get cell widget
tableWidget:setCellWidget(0, 0, widget)             -- Set cell widget
tableWidget:removeCellWidget(0, 0)                   -- Remove cell widget
tableWidget:setColumnHidden(0, true)                -- Hide/show column
tableWidget:setRowHidden(0, true)                    -- Hide/show row
```

---

## Container Widgets

### QTabWidget (Tabbed Interface)
```lua
-- Properties
tabWidget.currentIndex = 2                           -- Set/get current tab index
local count = tabWidget.count                        -- Get number of tabs
```

### QStackedWidget (Stacked Pages)
```lua
-- Properties
stackedWidget.currentIndex = 1                       -- Set/get current page index
```

### QMdiArea (Multiple Document Interface)
```lua
-- Instance Methods
mdiArea:activeSubWindow()                            -- Get active sub window
mdiArea:addSubWindow(widget)                         -- Add sub window
mdiArea:subWindowList()                              -- Get all sub windows

-- QMdiSubWindow Methods
subWindow:widget()                                   -- Get the contained widget
```

---

## Layout Management

### QLayout (Base Layout)
```lua
-- Instance Methods
layout:addWidget(widget)                             -- Add widget to layout
layout:count()                                       -- Get number of widgets
```

### Layout Types
```lua
-- Constructors (create new layouts)
local hboxLayout = QHBoxLayout()                     -- Horizontal box layout
local vboxLayout = QVBoxLayout()                     -- Vertical box layout

-- QWidget Layout Management
widget:setLayout(layout)                             -- Set widget's layout
```

---

## Window & Application

### QMainWindow
```lua
-- Instance Methods
mainWindow:setCentralWidget(widget)                  -- Set central widget
mainWindow:menuBar()                                 -- Get menu bar
mainWindow:addDockWidget(area, dockWidget)          -- Add dock widget
mainWindow:setUnifiedTitleAndToolBarOnMac(true)     -- Mac-specific setting
```

### QMenuBar & QMenu
```lua
-- QMenuBar Methods
menuBar:clear()                                      -- Clear all menus
menuBar:actionAt(point)                              -- Get action at point
menuBar:addMenu(menu)                                -- Add existing menu
menuBar:addMenuStr("Menu Name")                      -- Add menu by name

-- QMenu Methods  
menu:setTitle("Menu Title")                          -- Set menu title
menu:addActionStr("Action Name")                     -- Add action by name
```

### QAction (Menu/Toolbar Actions)
```lua
-- Instance Methods
action:setText("Action Text")                        -- Set action text
action:setIcon(icon)                                 -- Set action icon
action:trigger()                                     -- Trigger action programmatically
```

---

## Events & Testing

### Event Properties
```lua
-- QEvent Properties
local eventType = event.type                         -- Get event type

-- QMouseEvent Properties
local x = mouseEvent.x                               -- Get mouse X position
local y = mouseEvent.y                               -- Get mouse Y position  
local button = mouseEvent.button                     -- Get mouse button

-- QWheelEvent Properties
local delta = wheelEvent.delta                       -- Get wheel delta
local angleDelta = wheelEvent.angleDelta             -- Get angle delta
local pixelDelta = wheelEvent.pixelDelta             -- Get pixel delta
```

### UI Testing Functions
```lua
-- Global Testing Functions
mouseClick(widget, Qt.MouseButton.LeftButton, Qt.KeyboardModifier.NoModifier, QPoint(10, 10), 100)
mouseDoubleClick(widget, Qt.MouseButton.LeftButton, Qt.KeyboardModifier.NoModifier, QPoint(10, 10), 100)
keyClicks(widget, "Hello", Qt.KeyboardModifier.NoModifier, 100)
```

### OpenGL Widget
```lua
-- Instance Methods
openglWidget:setInitGLFunction(function)             -- Set OpenGL init function
openglWidget:setPaintGLFunction(function)            -- Set OpenGL paint function
openglWidget:setResizeGLFunction(function)           -- Set OpenGL resize function
openglWidget:setEventHandlerFunction(function)      -- Set event handler
openglWidget:getSelectedNames()                      -- Get selected object names
openglWidget:pick(x, y)                              -- Pick object at coordinates
openglWidget:getDevicePixelRatio()                   -- Get device pixel ratio
openglWidget:makeCurrent()                           -- Make OpenGL context current
```

---

## Data Types

### QString
```lua
-- Instance Methods
qstring:isEmpty()                                    -- Check if string is empty
qstring:toStdString()                                -- Convert to std::string
```

### QStringList
```lua
-- Static Functions
QStringList.fromStdVector({"item1", "item2"})       -- Create from vector
QStringList.toStdVector(qstringlist)                -- Convert to vector
```

### Date & Time Types
```lua
-- QDateTime Properties
dateTime.date = QDate()                              -- Set/get date component
dateTime.time = QTime()                              -- Set/get time component

-- QDate Properties & Methods
local day = date.day                                 -- Get day
local year = date.year                               -- Get year  
local month = date.month                             -- Get month
date:setDate(2024, 12, 25)                          -- Set date (year, month, day)

-- QTime Properties & Methods
local hour = time.hour                               -- Get hour
local minute = time.minute                           -- Get minute
local second = time.second                           -- Get second
local msec = time.msec                               -- Get milliseconds
time:setHMS(14, 30, 0)                               -- Set time (hour, minute, second)
```

### Item Types
```lua
-- QStandardItem Properties & Methods
item.text = "Item Text"                              -- Set/get item text
item:isChecked()                                     -- Check if checked
item:setCheckable(true)                              -- Set checkable state
item:isCheckable()                                   -- Check if checkable
item:setEnabled(true)                                -- Set enabled state
item:isEnabled()                                     -- Check if enabled
item:setEditable(true)                               -- Set editable state
item:isEditable()                                    -- Check if editable

-- QListWidgetItem Methods
listItem:text()                                      -- Get item text
listItem:setText("New Text")                         -- Set item text
listItem:isSelected()                                -- Check if selected
listItem:setSelected(true)                           -- Set selected state
listItem:setFlags(Qt.ItemFlag.ItemIsEditable)       -- Set item flags
listItem:flags()                                     -- Get item flags

-- QTableWidgetItem Methods
tableItem:text()                                     -- Get item text
tableItem:setText("New Text")                        -- Set item text
tableItem:setTextAlignment(alignment)               -- Set text alignment
tableItem:setFlags(Qt.ItemFlag.ItemIsEditable)      -- Set item flags
tableItem:flags()                                    -- Get item flags
```

---

## Constants

### Event Constants
```lua
-- QEvent Types
QEvent.MouseButtonPress                              -- Mouse button press
QEvent.MouseButtonRelease                            -- Mouse button release
QEvent.MouseButtonDblClick                           -- Mouse double click
QEvent.MouseMove                                     -- Mouse move
QEvent.KeyPress                                      -- Key press
QEvent.KeyRelease                                    -- Key release
QEvent.Wheel                                         -- Mouse wheel
```

### Mouse & Keyboard Constants  
```lua
-- Mouse Buttons
Qt.MouseButton.LeftButton                            -- Left mouse button
Qt.MouseButton.RightButton                           -- Right mouse button
Qt.MouseButton.MiddleButton                          -- Middle mouse button

-- Keyboard Modifiers
Qt.KeyboardModifier.NoModifier                       -- No modifier key
Qt.KeyboardModifier.ControlModifier                  -- Control key
Qt.KeyboardModifier.AltModifier                      -- Alt key
Qt.KeyboardModifier.ShiftModifier                    -- Shift key
```

### Dialog Constants
```lua
-- QMessageBox Buttons
QMessageBox.Ok                                       -- OK button
QMessageBox.Cancel                                   -- Cancel button
QMessageBox.Yes                                      -- Yes button
QMessageBox.No                                       -- No button
QMessageBox.Save                                     -- Save button
QMessageBox.Discard                                  -- Discard button
QMessageBox.Apply                                    -- Apply button
QMessageBox.Reset                                    -- Reset button

-- Item Flags
Qt.ItemFlag.ItemIsEditable                           -- Item can be edited
```

---

## Complete Examples

### Basic Window Creation
```lua
-- Create main window
local mainWindow = QMainWindow()
mainWindow:setWindowTitle("My Application")
mainWindow:resize(800, 600)

-- Create central widget
local centralWidget = QWidget()
mainWindow:setCentralWidget(centralWidget)

-- Show window
mainWindow:show()
```

### Form with Input Controls
```lua
-- Load UI from file
local form = loadUi("myform.ui")

-- Find controls by name
local nameEdit = findChild(form, "nameLineEdit")
local ageSpinBox = findChild(form, "ageSpinBox")  
local submitButton = findChild(form, "submitButton")

-- Set initial values
nameEdit.text = "Enter your name"
ageSpinBox.value = 25

-- Connect button click
connect(submitButton, "clicked()", function()
    local name = nameEdit.text
    local age = ageSpinBox.value
    popMsg("Hello " .. name .. ", you are " .. age .. " years old!")
end)

-- Show form
form:show()
```

### Dynamic Table Creation
```lua
-- Create table widget
local table = QTableWidget()
table:setRowCount(3)
table:setColumnCount(2) 
table:setHorizontalHeaderLabels({"Name", "Age"})

-- Add data
local nameItem = QTableWidgetItem("John")
local ageItem = QTableWidgetItem("25")
table:setItem(0, 0, nameItem)
table:setItem(0, 1, ageItem)

-- Auto-resize columns
table:resizeColumnsToContents()

-- Show table
table:show()
```

### Signal Connection Examples
```lua
-- Button click
connect(button, "clicked()", function()
    popMsg("Button clicked!")
end)

-- List selection change
connect(listWidget, "currentRowChanged(int)", function(row)
    popMsg("Selected row: " .. row)
end)

-- Text change
connect(lineEdit, "textChanged(QString)", function(text)
    print("Text changed to: " .. text)
end)
``` 