# FlexTerm Q&A Agent with RAG

A Python-based question and answer agent that serves as a web API for FlexTerm-related queries. The agent uses **RAG (Retrieval-Augmented Generation)** architecture with local embedding models (Ollama) and vector database for efficient document retrieval, combined with OpenAI GPT-4o for response generation.

## 🚀 Features

- **RAG Architecture**: Combines document retrieval with LLM generation for accurate, context-aware responses
- **Local Embeddings**: Uses Ollama `nomic-embed-text` model with OpenAI fallback
- **Vector Database**: Chroma for efficient semantic search
- **Dual Response Format**: Provides both verbose and concise answers
- **Multi-language Support**: Supports both English and Chinese queries
- **Source Attribution**: Shows which documents were used to generate answers
- **FastAPI Web Interface**: RESTful API with automatic documentation

## 📋 Prerequisites

1. **Python 3.8+**
2. **Ollama** installed and running with `nomic-embed-text:latest` model
3. **OpenAI API Key** for GPT-4o access

## 🛠️ Installation

1. **Clone/Navigate to the project directory**:
   ```bash
   cd flexterm-jira
   ```

2. **Create and activate virtual environment**:
   ```bash
   python -m venv venv
   # On Windows:
   venv\Scripts\activate
   # On macOS/Linux:
   source venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Verify Ollama setup**:
   ```bash
   ollama list
   # Ensure nomic-embed-text:latest is available
   ```

## 📚 Initial Setup

1. **Index the documents** (one-time setup):
   ```bash
   python scripts/index_documents.py
   ```
   
   This will:
   - Process all PDF and Markdown files in the `/doc/` directory
   - Create text chunks with 1024 tokens and 100-token overlap
   - Generate embeddings using Ollama
   - Store everything in the Chroma vector database

2. **Verify indexing**:
   The script will show how many documents were indexed and run a test search.

## 🚀 Running the Service

1. **Start the API server**:
   ```bash
   python main.py
   ```

2. **Access the service**:
   - **API Documentation**: http://localhost:8000/docs
   - **Health Check**: http://localhost:8000/api/v1/health
   - **Service Info**: http://localhost:8000/

## 📖 API Usage

### Ask a Question

**POST** `/api/v1/ask`

```json
{
  "question": "How do I configure a quay crane in FlexTerm?"
}
```

**Response**:
```json
{
  "status": "success",
  "response": {
    "verbose": "### Verbose Response\n[AI Response]\nDetailed explanation...",
    "concise": "### Concise Response\n- Brief point 1\n- Brief point 2"
  },
  "sources": [
    {
      "file": "FXTMANUAL_documentation-with-image.pdf",
      "page": 42,
      "section": "Quay Crane Configuration"
    }
  ],
  "retrieval_info": {
    "chunks_retrieved": 5,
    "relevance_scores": [0.89, 0.82, 0.78, 0.75, 0.71],
    "embedding_source": "ollama"
  }
}
```

### Health Check

**GET** `/api/v1/health`

Returns service status, Ollama availability, and vector database readiness.

### Statistics

**GET** `/api/v1/stats`

Returns knowledge base statistics and system information.

## 🔧 Configuration

Configuration is managed through environment variables in `.env`:

```env
# Required
OPENAI_API_KEY=your_openai_api_key

# Optional (defaults shown)
OLLAMA_BASE_URL=http://localhost:11434
EMBEDDING_MODEL=nomic-embed-text:latest
CHROMA_DB_PATH=./data/chroma_db
RETRIEVAL_TOP_K=7
CHUNK_SIZE=1024
CHUNK_OVERLAP=100
LOG_LEVEL=INFO
API_HOST=0.0.0.0
API_PORT=8000
```

## 📁 Project Structure

```
flexterm-jira/
├── app/
│   ├── api/
│   │   └── routes.py          # API endpoints
│   ├── core/
│   │   ├── config.py          # Configuration management
│   │   ├── embeddings.py      # Ollama/OpenAI embedding service
│   │   └── llm.py             # OpenAI GPT-4o integration
│   ├── services/
│   │   ├── document_processor.py  # PDF/Markdown processing
│   │   ├── vector_store.py        # Chroma vector database
│   │   ├── retrieval.py           # RAG retrieval logic
│   │   └── qa_service.py          # Main Q&A orchestration
│   └── models/
│       └── schemas.py         # Pydantic models
├── scripts/
│   └── index_documents.py     # Document indexing script
├── data/
│   └── chroma_db/            # Vector database storage
├── doc/                      # FlexTerm documentation
├── main.py                   # FastAPI application
├── requirements.txt          # Dependencies
└── .env                      # Environment configuration
```

## 🧪 Testing

The system includes several ways to test functionality:

1. **Document Indexing Test**: Run `python scripts/index_documents.py`
2. **API Health Check**: GET `/api/v1/health`
3. **Interactive API Docs**: Visit `/docs` for testing endpoints
4. **Manual Testing**: Use the `/ask` endpoint with sample questions

## 🔍 Troubleshooting

### Common Issues

1. **Ollama not available**:
   - Ensure Ollama is running: `ollama serve`
   - Check if model is available: `ollama list`
   - Pull model if needed: `ollama pull nomic-embed-text:latest`

2. **No documents indexed**:
   - Run the indexing script: `python scripts/index_documents.py`
   - Check if documents exist in `/doc/` directory

3. **OpenAI API errors**:
   - Verify API key in `.env` file
   - Check API quota and billing

4. **Vector database issues**:
   - Delete and recreate: Remove `/data/chroma_db/` and reindex

## 📝 Response Format

All responses follow the format specified in `prompt.md`:

- **[AI Response]** prefix
- **Verbose Response**: Detailed, professional explanation
- **Concise Response**: Brief bullet points (max 5 points, max 200 words)
- **Language matching**: Responds in the same language as the question
- **Source attribution**: References to specific documents and sections

## 🎯 Performance

- **Local embeddings**: ~10-50ms per query (no network latency)
- **Vector search**: Sub-second retrieval from thousands of documents
- **Response generation**: 1-3 seconds depending on context complexity
- **Concurrent requests**: Supports multiple simultaneous queries

## 📄 License

This project is part of the FlexTerm ecosystem. Please refer to FlexTerm licensing terms.
