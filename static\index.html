<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FlexTerm Q&A Assistant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .question-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
            transition: border-color 0.3s ease;
        }

        .question-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #667eea;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .response-section {
            display: none;
            margin-top: 30px;
        }

        .response-tabs {
            display: flex;
            border-bottom: 2px solid #e0e0e0;
            margin-bottom: 20px;
        }

        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .tab.active {
            color: #667eea;
            border-bottom: 2px solid #667eea;
        }

        .tab-content {
            display: none;
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .tab-content.active {
            display: block;
        }

        .response-text {
            line-height: 1.6;
            color: #2c3e50;
        }

        .sources {
            margin-top: 25px;
            padding: 20px;
            background: #e8f4fd;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .sources h4 {
            color: #2980b9;
            margin-bottom: 10px;
        }

        .source-item {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            font-size: 14px;
            color: #555;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin-top: 20px;
        }

        .sample-questions {
            margin-top: 30px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .sample-questions h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .sample-question {
            background: white;
            padding: 12px;
            margin: 8px 0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid #667eea;
        }

        .sample-question:hover {
            background: #e8f4fd;
            transform: translateX(5px);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .main-content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 FlexTerm Q&A Assistant</h1>
            <p>Ask questions about FlexTerm documentation and get instant, accurate answers</p>
        </div>

        <div class="main-content">
            <div class="input-section">
                <textarea 
                    id="questionInput" 
                    class="question-input" 
                    placeholder="Ask your FlexTerm question here... (e.g., 'What is FlexTerm?', 'How do I configure crane sequences?')"
                ></textarea>
                
                <div class="button-group">
                    <button id="askButton" class="btn btn-primary">
                        🚀 Ask Question
                    </button>
                    <button id="clearButton" class="btn btn-secondary">
                        🗑️ Clear
                    </button>
                </div>
            </div>

            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>Processing your question...</p>
            </div>

            <div id="responseSection" class="response-section">
                <div class="response-tabs">
                    <button class="tab active" data-tab="verbose">📝 Detailed Answer</button>
                    <button class="tab" data-tab="concise">📋 Quick Summary</button>
                </div>

                <div id="verboseTab" class="tab-content active">
                    <div id="verboseResponse" class="response-text"></div>
                </div>

                <div id="conciseTab" class="tab-content">
                    <div id="conciseResponse" class="response-text"></div>
                </div>

                <div id="sources" class="sources">
                    <h4>📚 Sources</h4>
                    <div id="sourcesList"></div>
                </div>
            </div>

            <div id="error" class="error" style="display: none;"></div>

            <div class="sample-questions">
                <h3>💡 Try these sample questions:</h3>
                <div class="sample-question" data-question="What is FlexTerm?">
                    What is FlexTerm?
                </div>
                <div class="sample-question" data-question="How do I configure crane sequences?">
                    How do I configure crane sequences?
                </div>
                <div class="sample-question" data-question="What are the main features of FlexTerm?">
                    What are the main features of FlexTerm?
                </div>
                <div class="sample-question" data-question="How does FlexTerm integrate with TOS systems?">
                    How does FlexTerm integrate with TOS systems?
                </div>
                <div class="sample-question" data-question="What programming languages does FlexTerm support?">
                    What programming languages does FlexTerm support?
                </div>
            </div>
        </div>
    </div>

    <script>
        // DOM elements
        const questionInput = document.getElementById('questionInput');
        const askButton = document.getElementById('askButton');
        const clearButton = document.getElementById('clearButton');
        const loading = document.getElementById('loading');
        const responseSection = document.getElementById('responseSection');
        const verboseResponse = document.getElementById('verboseResponse');
        const conciseResponse = document.getElementById('conciseResponse');
        const sourcesList = document.getElementById('sourcesList');
        const errorDiv = document.getElementById('error');
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');
        const sampleQuestions = document.querySelectorAll('.sample-question');

        // Event listeners
        askButton.addEventListener('click', askQuestion);
        clearButton.addEventListener('click', clearAll);
        questionInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                askQuestion();
            }
        });

        // Tab switching
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;
                switchTab(tabName);
            });
        });

        // Sample questions
        sampleQuestions.forEach(question => {
            question.addEventListener('click', () => {
                questionInput.value = question.dataset.question;
                questionInput.focus();
            });
        });

        function switchTab(tabName) {
            tabs.forEach(tab => tab.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            document.getElementById(`${tabName}Tab`).classList.add('active');
        }

        async function askQuestion() {
            const question = questionInput.value.trim();
            
            if (!question) {
                showError('Please enter a question.');
                return;
            }

            // Show loading state
            loading.style.display = 'block';
            responseSection.style.display = 'none';
            errorDiv.style.display = 'none';
            askButton.disabled = true;

            try {
                const response = await fetch('/api/v1/ask', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ question: question })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                
                if (data.status === 'success') {
                    displayResponse(data);
                } else {
                    throw new Error(data.message || 'Unknown error occurred');
                }

            } catch (error) {
                console.error('Error:', error);
                showError(`Failed to get response: ${error.message}`);
            } finally {
                loading.style.display = 'none';
                askButton.disabled = false;
            }
        }

        function displayResponse(data) {
            // Display responses
            verboseResponse.innerHTML = formatResponse(data.response.verbose);
            conciseResponse.innerHTML = formatResponse(data.response.concise);

            // Display sources
            if (data.sources && data.sources.length > 0) {
                sourcesList.innerHTML = data.sources.map(source => `
                    <div class="source-item">
                        📄 ${source.file}${source.page ? ` (Page ${source.page})` : ''}
                        ${source.section ? ` - ${source.section}` : ''}
                    </div>
                `).join('');
            } else {
                sourcesList.innerHTML = '<div class="source-item">No specific sources found</div>';
            }

            responseSection.style.display = 'block';
            responseSection.scrollIntoView({ behavior: 'smooth' });
        }

        function formatResponse(text) {
            // Convert markdown-like formatting to HTML
            return text
                .replace(/### (.*)/g, '<h3>$1</h3>')
                .replace(/## (.*)/g, '<h2>$1</h2>')
                .replace(/# (.*)/g, '<h1>$1</h1>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
                .replace(/- (.*)/g, '<li>$1</li>')
                .replace(/\n\n/g, '</p><p>')
                .replace(/^(.*)$/gm, '<p>$1</p>')
                .replace(/<p><li>/g, '<ul><li>')
                .replace(/<\/li><\/p>/g, '</li></ul>');
        }

        function showError(message) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            responseSection.style.display = 'none';
        }

        function clearAll() {
            questionInput.value = '';
            responseSection.style.display = 'none';
            errorDiv.style.display = 'none';
            questionInput.focus();
        }

        // Focus on input when page loads
        window.addEventListener('load', () => {
            questionInput.focus();
        });
    </script>
</body>
</html>
