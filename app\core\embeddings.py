"""Embedding service with Ollama primary and OpenAI fallback."""

import logging
import asyncio
from typing import List, Optional, Tuple
import numpy as np
import ollama
import openai
from openai import OpenAI

from .config import settings

logger = logging.getLogger(__name__)


class EmbeddingService:
    """Embedding service with Ollama primary and OpenAI fallback."""
    
    def __init__(self):
        self.ollama_client = None
        self.openai_client = None
        self.ollama_available = False
        self.embedding_dimension = None
        self._initialize_clients()
    
    def _initialize_clients(self):
        """Initialize embedding clients."""
        # Initialize OpenAI client
        try:
            self.openai_client = OpenAI(api_key=settings.openai_api_key)
            logger.info("OpenAI client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
        
        # Check Ollama availability
        self._check_ollama_availability()
    
    def _check_ollama_availability(self) -> bool:
        """Check if Ollama is available and has the required model."""
        try:
            # Test Ollama connection
            models = ollama.list()
            model_names = [model['name'] for model in models.get('models', [])]
            
            if settings.embedding_model in model_names:
                self.ollama_available = True
                self.embedding_dimension = 768  # nomic-embed-text dimension
                logger.info(f"Ollama available with model: {settings.embedding_model}")
                return True
            else:
                logger.warning(f"Ollama model {settings.embedding_model} not found. Available models: {model_names}")
                
        except Exception as e:
            logger.warning(f"Ollama not available: {e}")
        
        self.ollama_available = False
        return False
    
    async def generate_embeddings(self, texts: List[str]) -> Tuple[List[List[float]], str]:
        """
        Generate embeddings for a list of texts.
        
        Args:
            texts: List of text strings to embed
            
        Returns:
            Tuple of (embeddings, source) where source is 'ollama' or 'openai'
        """
        if not texts:
            return [], "none"
        
        # Try Ollama first
        if self.ollama_available:
            try:
                return await self._generate_ollama_embeddings(texts), "ollama"
            except Exception as e:
                logger.warning(f"Ollama embedding failed, falling back to OpenAI: {e}")
                self.ollama_available = False
        
        # Fallback to OpenAI
        if self.openai_client:
            try:
                return await self._generate_openai_embeddings(texts), "openai"
            except Exception as e:
                logger.error(f"OpenAI embedding failed: {e}")
                raise Exception("Both Ollama and OpenAI embedding services failed")
        
        raise Exception("No embedding service available")
    
    async def _generate_ollama_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings using Ollama."""
        embeddings = []
        
        for text in texts:
            try:
                response = ollama.embeddings(
                    model=settings.embedding_model,
                    prompt=text
                )
                embeddings.append(response['embedding'])
            except Exception as e:
                logger.error(f"Failed to generate Ollama embedding for text: {e}")
                raise
        
        return embeddings
    
    async def _generate_openai_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings using OpenAI."""
        try:
            response = self.openai_client.embeddings.create(
                model="text-embedding-3-small",
                input=texts
            )
            
            embeddings = [data.embedding for data in response.data]
            self.embedding_dimension = len(embeddings[0]) if embeddings else 1536
            
            return embeddings
            
        except Exception as e:
            logger.error(f"Failed to generate OpenAI embeddings: {e}")
            raise
    
    async def generate_single_embedding(self, text: str) -> Tuple[List[float], str]:
        """Generate embedding for a single text."""
        embeddings, source = await self.generate_embeddings([text])
        return embeddings[0] if embeddings else [], source
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings."""
        if self.embedding_dimension is None:
            if self.ollama_available:
                return 768  # nomic-embed-text dimension
            else:
                return 1536  # OpenAI text-embedding-3-small dimension
        return self.embedding_dimension
    
    def is_available(self) -> bool:
        """Check if any embedding service is available."""
        return self.ollama_available or (self.openai_client is not None)


# Global embedding service instance
embedding_service = EmbeddingService()


def get_embedding_service() -> EmbeddingService:
    """Get the global embedding service instance."""
    return embedding_service
