"""Document processing service for PDF and Markdown files."""

import logging
import os
import hashlib
from typing import List, Dict, Any, Tuple
import pdfplumber
from langchain.text_splitter import RecursiveCharacterTextSplitter

from app.core.config import settings

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """Document processing service for PDF and Markdown files."""
    
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", " ", ""]
        )
    
    def process_pdf(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Process PDF file and extract text with metadata.
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            List of document chunks with metadata
        """
        try:
            chunks = []
            filename = os.path.basename(file_path)
            
            with pdfplumber.open(file_path) as pdf:
                logger.info(f"Processing PDF: {filename} ({len(pdf.pages)} pages)")
                
                for page_num, page in enumerate(pdf.pages, 1):
                    # Extract text from page
                    text = page.extract_text()
                    
                    if text and text.strip():
                        # Clean and normalize text
                        text = self._clean_text(text)
                        
                        # Split into chunks
                        page_chunks = self.text_splitter.split_text(text)
                        
                        # Create metadata for each chunk
                        for chunk_idx, chunk in enumerate(page_chunks):
                            if chunk.strip():  # Only add non-empty chunks
                                chunk_id = self._generate_chunk_id(filename, page_num, chunk_idx)
                                
                                chunks.append({
                                    'id': chunk_id,
                                    'text': chunk.strip(),
                                    'metadata': {
                                        'source_file': filename,
                                        'file_type': 'pdf',
                                        'page_number': page_num,
                                        'chunk_index': chunk_idx,
                                        'total_pages': len(pdf.pages)
                                    }
                                })
            
            logger.info(f"Extracted {len(chunks)} chunks from PDF: {filename}")
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to process PDF {file_path}: {e}")
            return []
    
    def process_markdown(self, file_path: str) -> List[Dict[str, Any]]:
        """
        Process Markdown file and extract text with metadata.
        
        Args:
            file_path: Path to Markdown file
            
        Returns:
            List of document chunks with metadata
        """
        try:
            chunks = []
            filename = os.path.basename(file_path)
            
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            logger.info(f"Processing Markdown: {filename}")
            
            # Clean and normalize text
            content = self._clean_text(content)
            
            # Split into chunks
            text_chunks = self.text_splitter.split_text(content)
            
            # Create metadata for each chunk
            for chunk_idx, chunk in enumerate(text_chunks):
                if chunk.strip():  # Only add non-empty chunks
                    chunk_id = self._generate_chunk_id(filename, 1, chunk_idx)
                    
                    # Try to extract section information
                    section = self._extract_section_from_chunk(chunk)
                    
                    chunks.append({
                        'id': chunk_id,
                        'text': chunk.strip(),
                        'metadata': {
                            'source_file': filename,
                            'file_type': 'markdown',
                            'chunk_index': chunk_idx,
                            'section': section
                        }
                    })
            
            logger.info(f"Extracted {len(chunks)} chunks from Markdown: {filename}")
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to process Markdown {file_path}: {e}")
            return []
    
    def process_directory(self, directory_path: str) -> List[Dict[str, Any]]:
        """
        Process all supported files in a directory.
        
        Args:
            directory_path: Path to directory containing documents
            
        Returns:
            List of all document chunks with metadata
        """
        all_chunks = []
        
        if not os.path.exists(directory_path):
            logger.error(f"Directory does not exist: {directory_path}")
            return all_chunks
        
        # Supported file extensions
        supported_extensions = {'.pdf', '.md', '.markdown'}
        
        for filename in os.listdir(directory_path):
            file_path = os.path.join(directory_path, filename)
            
            if os.path.isfile(file_path):
                file_ext = os.path.splitext(filename)[1].lower()
                
                if file_ext == '.pdf':
                    chunks = self.process_pdf(file_path)
                    all_chunks.extend(chunks)
                elif file_ext in {'.md', '.markdown'}:
                    chunks = self.process_markdown(file_path)
                    all_chunks.extend(chunks)
                else:
                    logger.debug(f"Skipping unsupported file: {filename}")
        
        logger.info(f"Processed directory {directory_path}: {len(all_chunks)} total chunks")
        return all_chunks
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        # Remove excessive whitespace
        text = ' '.join(text.split())
        
        # Remove common PDF artifacts
        text = text.replace('\x00', '')  # Null characters
        text = text.replace('\ufffd', '')  # Replacement characters
        
        return text
    
    def _extract_section_from_chunk(self, chunk: str) -> str:
        """Extract section title from markdown chunk."""
        lines = chunk.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('#'):
                # Remove markdown header symbols and return section title
                return line.lstrip('#').strip()
        return "Unknown Section"
    
    def _generate_chunk_id(self, filename: str, page_or_section: int, chunk_index: int) -> str:
        """Generate unique ID for a document chunk."""
        # Create a unique identifier based on file, page/section, and chunk index
        base_string = f"{filename}_{page_or_section}_{chunk_index}"
        return hashlib.md5(base_string.encode()).hexdigest()


# Global document processor instance
document_processor = DocumentProcessor()


def get_document_processor() -> DocumentProcessor:
    """Get the global document processor instance."""
    return document_processor
