"""Pydantic models for request/response schemas."""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class QuestionRequest(BaseModel):
    """Request model for Q&A endpoint."""
    question: str = Field(..., description="User question about FlexTerm", min_length=1, max_length=1000)


class SourceInfo(BaseModel):
    """Information about document source."""
    file: str = Field(..., description="Source file name")
    section: Optional[str] = Field(None, description="Document section")
    page: Optional[int] = Field(None, description="Page number (for PDFs)")


class RetrievalInfo(BaseModel):
    """Information about retrieval process."""
    chunks_retrieved: int = Field(..., description="Number of chunks retrieved")
    relevance_scores: List[float] = Field(..., description="Relevance scores for retrieved chunks")
    embedding_source: str = Field(..., description="Embedding model used (ollama/openai)")


class ResponseData(BaseModel):
    """Response data containing verbose and concise answers."""
    verbose: str = Field(..., description="Detailed verbose response")
    concise: str = Field(..., description="Brief concise response")


class QuestionResponse(BaseModel):
    """Response model for Q&A endpoint."""
    status: str = Field(..., description="Response status")
    response: ResponseData = Field(..., description="Formatted response data")
    sources: List[SourceInfo] = Field(..., description="Source documents used")
    retrieval_info: RetrievalInfo = Field(..., description="Retrieval process information")


class ErrorResponse(BaseModel):
    """Error response model."""
    status: str = Field(default="error", description="Response status")
    message: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str = Field(..., description="Service status")
    version: str = Field(..., description="Application version")
    ollama_available: bool = Field(..., description="Ollama service availability")
    vector_db_ready: bool = Field(..., description="Vector database readiness")
