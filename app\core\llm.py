"""LLM integration service with OpenAI GPT-4o."""

import logging
import os
from typing import <PERSON><PERSON>, <PERSON><PERSON>
from openai import OpenAI

from app.core.config import settings

logger = logging.getLogger(__name__)


class LLMService:
    """Service for interacting with OpenAI GPT-4o."""
    
    def __init__(self):
        self.client = None
        self.system_prompt = ""
        self._initialize_client()
        self._load_system_prompt()
    
    def _initialize_client(self):
        """Initialize OpenAI client."""
        try:
            self.client = OpenAI(api_key=settings.openai_api_key)
            logger.info("OpenAI client initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            raise
    
    def _load_system_prompt(self):
        """Load system prompt from prompt.md file."""
        try:
            if os.path.exists(settings.prompt_file):
                with open(settings.prompt_file, 'r', encoding='utf-8') as file:
                    self.system_prompt = file.read()
                logger.info(f"System prompt loaded from: {settings.prompt_file}")
            else:
                logger.warning(f"Prompt file not found: {settings.prompt_file}")
                self.system_prompt = self._get_default_prompt()
        except Exception as e:
            logger.error(f"Failed to load system prompt: {e}")
            self.system_prompt = self._get_default_prompt()
    
    def _get_default_prompt(self) -> str:
        """Get default system prompt if file is not available."""
        return """You are an intelligent assistant specialized in answering user questions related to the FlexTerm Open Platform (OP) simulation and emulation tool.

Rules:
1. Only respond to questions related to FlexTerm simulation and emulation modeling.
2. Use the same language as the user (reply in Chinese to Chinese questions, and in English to English questions).
3. Be accurate, easy to understand, and professional.
4. Add "[AI Response]" at the top of your response.
5. Provide both Verbose and Concise responses as specified.

Response Format:
### Verbose Response
[AI Response]
Professional, structural, well-organized paragraphs

### Concise Response
- Use plain dashes for bullets
- Natural human phrasing
- Only critical actionable information
- Max 200 words total"""
    
    async def generate_response(
        self, 
        question: str, 
        context: str = ""
    ) -> Tuple[str, str]:
        """
        Generate response using GPT-4o with RAG context.
        
        Args:
            question: User question
            context: Retrieved context from RAG
            
        Returns:
            Tuple of (verbose_response, concise_response)
        """
        try:
            # Construct the prompt with context
            user_prompt = self._construct_user_prompt(question, context)
            
            # Generate response
            response = self.client.chat.completions.create(
                model=settings.openai_model,
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                max_tokens=settings.max_tokens,
                temperature=0.1,  # Low temperature for consistent, factual responses
                top_p=0.9
            )
            
            full_response = response.choices[0].message.content
            
            # Parse the response into verbose and concise parts
            verbose_response, concise_response = self._parse_response(full_response)
            
            logger.info("Successfully generated LLM response")
            
            return verbose_response, concise_response
            
        except Exception as e:
            logger.error(f"Failed to generate LLM response: {e}")
            return self._get_error_response(str(e))
    
    def _construct_user_prompt(self, question: str, context: str) -> str:
        """Construct user prompt with question and context."""
        if context:
            prompt = f"""Based on the following FlexTerm documentation context, please answer the user's question.

CONTEXT:
{context}

USER QUESTION:
{question}

Please provide your response in the exact format specified in the system prompt, with both Verbose and Concise responses."""
        else:
            prompt = f"""USER QUESTION:
{question}

Please provide your response in the exact format specified in the system prompt, with both Verbose and Concise responses. Note: No specific documentation context was found for this question."""
        
        return prompt
    
    def _parse_response(self, full_response: str) -> Tuple[str, str]:
        """Parse LLM response into verbose and concise parts."""
        try:
            # Look for the response sections
            verbose_start = full_response.find("### Verbose Response")
            concise_start = full_response.find("### Concise Response")
            
            if verbose_start == -1 or concise_start == -1:
                # If sections not found, try alternative parsing
                return self._fallback_parse_response(full_response)
            
            # Extract verbose response
            verbose_end = concise_start
            verbose_response = full_response[verbose_start:verbose_end].strip()
            
            # Extract concise response
            concise_response = full_response[concise_start:].strip()
            
            # Ensure proper formatting
            if not verbose_response.startswith("### Verbose Response"):
                verbose_response = "### Verbose Response\n" + verbose_response
            
            if not concise_response.startswith("### Concise Response"):
                concise_response = "### Concise Response\n" + concise_response
            
            return verbose_response, concise_response
            
        except Exception as e:
            logger.error(f"Failed to parse LLM response: {e}")
            return self._get_error_response("Response parsing failed")
    
    def _fallback_parse_response(self, full_response: str) -> Tuple[str, str]:
        """Fallback parsing when standard format is not found."""
        # If the response doesn't follow the expected format, create a basic structure
        verbose_response = f"### Verbose Response\n[AI Response]\n{full_response}"
        concise_response = "### Concise Response\n- Please refer to the verbose response above for detailed information"
        
        return verbose_response, concise_response
    
    def _get_error_response(self, error_message: str) -> Tuple[str, str]:
        """Generate error response in the expected format."""
        verbose_response = f"""### Verbose Response
[AI Response]
I apologize, but I encountered an error while processing your question about FlexTerm. The error was: {error_message}

Please try rephrasing your question or contact www.flexterm.com for assistance."""
        
        concise_response = """### Concise Response
- Error occurred while processing the question
- Please try rephrasing your question
- Contact www.flexterm.com for support"""
        
        return verbose_response, concise_response
    
    def is_available(self) -> bool:
        """Check if LLM service is available."""
        return self.client is not None


# Global LLM service instance
llm_service = LLMService()


def get_llm_service() -> LLMService:
    """Get the global LLM service instance."""
    return llm_service
