# FlexTerm Helper API Documentation

This document contains all FlexTerm Lua API helper modules combined into a single reference.

**Copyright**: Copyright (c) Moffatt & Nichol. All rights reserved.
**Author**: FlexTerm

## Table of Contents

- [CheHelper](#module-chehelper)
- [DispatcherHelper](#module-dispatcherhelper)
- [DualTrolleyQuayCraneHelper](#module-dualtrolleyquaycranehelper)
- [JobHelper](#module-jobhelper)
- [JobList](#module-joblist)
- [LaneHelper](#module-lanehelper)
- [List](#module-list)
- [Log](#module-log)
- [MoveHelper](#module-movehelper)
- [ParkingHelper](#module-parkinghelper)
- [SimTOSDispatcherHelper](#module-simtosdispatcherhelper)
- [SimTOSDispatchFunctions](#module-simtosdispatchfunctions)
- [SimTOSDualTrolleyQuayCraneHelper](#module-simtosdualtrolleyquaycranehelper)
- [SimTOSGantryCraneHelper](#module-simtosgantrycranehelper)
- [SimTOSJobHelper](#module-simtosjobhelper)
- [SimTOSQuayCraneHelper](#module-simtosquaycranehelper)
- [SimTOSScenarioHelper](#module-simtosscenariohelper)
- [SimTOSScoringFunctions](#module-simtosscoringfunctions)
- [SimTOSSingleStackPrimeMoverHelper](#module-simtossinglestackprimemoverhelper)
- [SimTOSVesselHelper](#module-simtosvesselhelper)
- [SimTOSYardBlockHelper](#module-simtosyardblockhelper)
- [SimTOSYardCraneHelper](#module-simtosyardcranehelper)

# Module `CheHelper`

Container handling equipment (CHE) helper functions.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[updateDestinationGuide (che, isPickup)](#updateDestinationGuide) Recalculate and update the destination guide of a CHE.

## []()Functions

[]()**updateDestinationGuide (che, isPickup)**

Recalculate and update the destination guide of a CHE. The destination guide is the location guide that a CHE's spreader should align with to transfer a container. For pickup operations, this is the center of the container(s). For dropoff operations, this is the center of the destination slot(s) for the container(s). This function is used to recalculate the location guide if the source container(s) (in the case of pickup) or destination slot(s) (in the case of dropoff) have changed since a job was started.

### Parameters:

- che The CHE object.
- isPickup Set to true if the destination guide should be calculated based on a pickup operation. False, if the destination guide should be calculated based on a dropoff operation.

### Returns:

1. destinationGuide The updated destination guide.

---

# Module `DispatcherHelper`

Dispatcher helper functions.

This file must be required by any other dispatcher functions file. This file creates the DispatchFunctions tablespace and defines the default dispatcher functions. After this file is required, then any other dispatch functions can be defined and added to the DispatchFunctions tablespace.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[addJobToLinkedListOfPendingJobs (dispatcher, job)](#addJobToLinkedListOfPendingJobs) Adds a job to the dispatcher job list (linked list). [getDispatcherType (dispatcher)](#getDispatcherType) Gets the type from the dispatcher's first worker. [getIdleWorkers (dispatcher)](#getIdleWorkers) Get all idle workers in the dispatcher's workers list property.

## []()Functions

[]()**addJobToLinkedListOfPendingJobs (dispatcher, job)**

Adds a job to the dispatcher job list (linked list).

### Parameters:

- dispatcher The dispatcher object.
- job The job to add.

[]()**getDispatcherType (dispatcher)**

Gets the type from the dispatcher's first worker.

### Parameters:

- dispatcher The dispatcher object.

### Returns:

1. typeName The type name of workers in the dispatcher's workers list.

[]()**getIdleWorkers (dispatcher)**

Get all idle workers in the dispatcher's workers list property.

### Parameters:

- dispatcher The dispatcher object.

### Returns:

1. idleWorkers The list of idle workers.

---

# Module `DualTrolleyQuayCraneHelper`

Helper functions for dual trolley quay crane state functions.

These are essentially functions for the primary and secondary trolleys.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[\_getNextAvailablePlatform (trolley, jobData)](#_getNextAvailablePlatform) Base function to get the next available platform location for the job. [getNextAvailablePlatform (trolley, jobData)](#getNextAvailablePlatform) Overrideable function to get the next available platform location for the job. [getPlatformLocation (trolley)](#getPlatformLocation) Get the platform location based on the current job. [hasPriorityToEnterPlatform (trolley)](#hasPriorityToEnterPlatform) Determine if the trolley has priority to enter the platform. [isOtherTrolleyInPlatform (trolley)](#isOtherTrolleyInPlatform) Check if the other trolley is currently in the platform. [isPlatformAvailable (trolley)](#isPlatformAvailable) Check if the trolley's currently selected platform location is available for the trolley to move to. [isPlatformLocationAvailable (trolley)](#isPlatformLocationAvailable) Check if the trolley's currently selected platform location is available. [isPlatformLocationOccupied (trolley)](#isPlatformLocationOccupied) Helper function to check if the trolley's platform location is occupied. [reservePlatform (trolley, reserve)](#reservePlatform) Reserve the platform for exclusive operation.

## [Tables](#Tables)

[LocationType](#LocationType) The location types. [PlatformLocation](#PlatformLocation) The platform locations. [TrolleyType](#TrolleyType) The trolley types.

## []()Functions

[]()**\_getNextAvailablePlatform (trolley, jobData)**

Base function to get the next available platform location for the job. By default, discharges are placed at the landside and loads are placed at the waterside.

### Parameters:

- trolley The trolley object.
- jobData The job data.

### Returns:

1. platformLocation The selected platform location for the job. Return nil if no platform location available.

[]()**getNextAvailablePlatform (trolley, jobData)**

Overrideable function to get the next available platform location for the job. The returned platform location will be set as the trolley's currently selected platform location. By default, discharges are placed at the landside and loads are placed at the waterside.

### Parameters:

- trolley The trolley object.
- jobData The job data.

### Returns:

1. platformLocation The selected platform location for the job. Return nil if no platform location available.

### Usage:

- ```
  -- example
  local isPrimary = DualTrolleyQuayCraneHelper.isPrimaryTrolley(trolley)
  local isSecondary = DualTrolleyQuayCraneHelper.isSecondaryTrolley(trolley)
  local isTandemOperation = JobHelper.isTandemOperation(jobData)
  if isTandemOperation then
    -- primary tandem operations are discharged to both slots
    if isPrimary then
      return DualTrolleyQuayCraneHelper.PlatformLocation.WaterAndLand
    elseif isSecondary then
      -- secondary tandem operations will be single lift loaded to each slot
      local isWatersideOccupied = DualTrolleyQuayCraneHelper.isWatersideOccupied(trolley)
      local isLandsideOccupied = DualTrolleyQuayCraneHelper.isLandsideOccupied(trolley)
      -- for tandem operations, first container always goes into the waterside cell, and second container goes into the landside cell
      -- check waterside first, landside second
      if not isWatersideOccupied then
        return DualTrolleyQuayCraneHelper.PlatformLocation.Waterside
      elseif not isLandsideOccupied then
        return DualTrolleyQuayCraneHelper.PlatformLocation.Landside
      end
    end
  elseif JobHelper.isDischarge(jobData) then
    return DualTrolleyQuayCraneHelper.PlatformLocation.Landside
  elseif JobHelper.isLoad(jobData) then
    return DualTrolleyQuayCraneHelper.PlatformLocation.Waterside
  end
  return nil
  ```

[]()**getPlatformLocation (trolley)**

Get the platform location based on the current job. If primay trolley tandem operation, then return DualTrolleyQuayCraneHelper.PlatformLocation.WaterAndLand. If discharge, primary trolley location is currently selected platform location; secondary trolley location is container's current platform location. If load, primary trolley location is container's current platform location; secondary trolley location is currently selected platform location.

### Parameters:

- trolley The trolley object.

### Returns:

1. platformLocation The platform location based on the current job. If unable to determine platform location, then return nil.

### See also:

- [DualTrolleyQuayCraneHelper.getNextAvailablePlatform](#module-dualtrolleyquaycranehelper#getNextAvailablePlatform)

[]()**hasPriorityToEnterPlatform (trolley)**

Determine if the trolley has priority to enter the platform. By default, prioritize primary trolley operations.

### Parameters:

- trolley The trolley object.

### Returns:

1. hasPriority Return true if the trolley has priority to enter the platform. False, otherwise.

[]()**isOtherTrolleyInPlatform (trolley)**

Check if the other trolley is currently in the platform.

### Parameters:

- trolley The trolley object.

### Returns:

1. boolean Return true if the other trolley is currently in the platform. False, otherwise.

[]()**isPlatformAvailable (trolley)**

Check if the trolley's currently selected platform location is available for the trolley to move to. This function is called when the trolley is requesting access to the platform. The trolley requests access to the platform in two cases: 1. Arrival at the decision point. 2. Arrival at the standby point, specifically if it has received a job while moving to the standby point (i.e., it was idle and received a new job). The following conditions must be met for the trolley to be able to move into the platform. 1. Trolley has priority to enter the platform. 2. The trolley's currently selected platform location is available. 3. Other trolley is not in the platform.

### Parameters:

- trolley The trolley object.

### Returns:

1. platformAvailable Return true if the platform is available and the trolley can move into the platform. False, otherwise.
2. hasPriority Return true if the trolley has priority to enter the platform. False, otherwise.
3. otherTrolleyInPlatform Return true if the other trolley is in the platform. False, otherwise.

### See also:

- [DualTrolleyQuayCraneHelper.hasPriorityToEnterPlatform](#module-dualtrolleyquaycranehelper#hasPriorityToEnterPlatform)
- [DualTrolleyQuayCraneHelper.isPlatformLocationAvailable](#module-dualtrolleyquaycranehelper#isPlatformLocationAvailable)
- [DualTrolleyQuayCraneHelper.isOtherTrolleyInPlatform](#module-dualtrolleyquaycranehelper#isOtherTrolleyInPlatform)

[]()**isPlatformLocationAvailable (trolley)**

Check if the trolley's currently selected platform location is available. The check depends on whether or not the trolley's platform location is occupied. If discharge, the primary trolley's location must not be occupied; the secondary trolley's location must be occupied. If load, the primary trolley's location must be occupied; the secondary trolley's location must not be occupied.

### Parameters:

- trolley The trolley object.

### Returns:

1. boolean True if the currently selected platform location is available. False, otherwise.

### See also:

- [DualTrolleyQuayCraneHelper.isPlatformLocationOccupied](#module-dualtrolleyquaycranehelper#isPlatformLocationOccupied)

[]()**isPlatformLocationOccupied (trolley)**

Helper function to check if the trolley's platform location is occupied. The platform location is based on the trolley's current job.

### Parameters:

- trolley The trolley object.

### Returns:

1. boolean Return true if the currently selected platform location is occupied. False, otherwise.

### See also:

- [DualTrolleyQuayCraneHelper.getPlatformLocation](#module-dualtrolleyquaycranehelper#getPlatformLocation)

[]()**reservePlatform (trolley, reserve)**

Reserve the platform for exclusive operation. If setting the platform reservation, this function will return true on successful reservation. If resetting the platform reservation, this function will return true on successful reset.

### Parameters:

- trolley The trolley object.
- reserve Pass true to try and reserve the platform. Pass false to clear the platform reservation.

### Returns:

1. boolean Returns true if successfully set/clear the platform reservation. False, otherwise.

## []()Tables

[]()**LocationType**

The location types.

### Fields:

- Vessel The vessel location.
- BetweenLegs The location between the legs of the quay crane.
- Platform The platform location.
- Backreach The backreach of the quay crane.

[]()**PlatformLocation**

The platform locations.

### Fields:

- Waterside The waterside location, which corresponds to the first cell in the platform.
- Landside The landside location, which corresponds to the second cell in the platform.
- WaterAndLand Both waterside and landside, which corresponds to both cells in the platform.

[]()**TrolleyType**

The trolley types.

### Fields:

- Primary The primary trolley.
- Secondary The secondary trolley.

---

# Module `JobHelper`

Helper functions for job data.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[isLoad (job)](#isLoad) Overrideable function to check for loading operation.

## []()Functions

[]()**isLoad (job)**

Overrideable function to check for loading operation.

### Parameters:

- job The job data.

### Returns:

1. boolean True if the job is a loading operation. False, otherwise

---

# Module `JobList`

Helper functions for maintaining a list of jobs.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[addJob (obj, jobListPropertyName, job)](#addJob) Add a job to a job list. [getJobsWithPropertySetTo (obj, jobListPropertyName, jobPropertyName, jobPropertyValue)](#getJobsWithPropertySetTo) Get all jobs that have a property set to a specific value in the job list. [removeJob (obj, jobListPropertyName, job)](#removeJob) Remove a job from the job list.

## []()Functions

[]()**addJob (obj, jobListPropertyName, job)**

Add a job to a job list. The list is a regular Lua list.

### Parameters:

- obj The object that owns the job list.
- jobListPropertyName The name of the job list property.
- job The job to add.

[]()**getJobsWithPropertySetTo (obj, jobListPropertyName, jobPropertyName, jobPropertyValue)**

Get all jobs that have a property set to a specific value in the job list.

### Parameters:

- obj The object with jobs.
- jobListPropertyName The name of the job list property.
- jobPropertyName The name of a job's property to search for.
- jobPropertyValue The value of the job's property to search for.

### Returns:

1. newJobList The list of jobs that have a property set to a specific value in the job list.

[]()**removeJob (obj, jobListPropertyName, job)**

Remove a job from the job list.

### Parameters:

- obj The object with jobs.
- jobListPropertyName The name of the job list property.
- job The job to remove.

---

# Module `LaneHelper`

Helper functions for multilane layout.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[createNewMultiLane (originX, originY, laneLength, rotationZ, totalLanes, laneWidth, laneDirection, prefix)](#createNewMultiLane) Create a new multilane with the specified parameters.

## []()Functions

[]()**createNewMultiLane (originX, originY, laneLength, rotationZ, totalLanes, laneWidth, laneDirection, prefix)**

Create a new multilane with the specified parameters.

### Parameters:

- originX The x-position of the multilane's origin.
- originY The y-position of the multilane's origin.
- laneLength The length of the multilane.
- rotationZ The z-rotation of the multilane.
- totalLanes The total number of lanes within the multilane. If not specified, this will default to 4.
- laneWidth The width of each lane in the multilane. If not specified, this will default to 4.
- laneDirection The travel direction of the lanes. -1 is negative direction, 0 is bi-directional, and 1 is positive direction. If not specified, then the first half of all lanes will be positive direction (1) and the second half of all lanes will be negative direction (-1).
- prefix The prefix to append to the created multilane index. If not specified, it will default to "ML."

### Returns:

1. multiLaneIndex The index of the created multilane.
2. multiLaneObject The mutlilane object.

---

# Module `List`

List helper functions.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[elements (list)](#elements) An iterator for elements in a list. [new ()](#new) Create a new list. [popleft (list)](#popleft) Remove and return and element from the left (or front) of the list. [size (list)](#size) Get the size of a List object.

## [Doubly LinkedList](#Doubly_LinkedList)

[LinkedList.isEmpty (list)](#LinkedList.isEmpty) Check if the specified linked list is empty. [LinkedList.nodes (list)](#LinkedList.nodes) Stateless iterator for a linked list. [LinkedList.size (list)](#LinkedList.size) Get the total number of elements in a linked list.

## []()Functions

[]()**elements (list)**

An iterator for elements in a list. Use generic for to iterate the elements in a list.

### Parameters:

- list The list to iterate.

### Returns:

1. iterator The iterator.
2. state The invariant state data for the iterator.

### Usage:

- ```
  for element in List.elements(list) do
    something
  end
  ```

[]()**new ()**

Create a new list. The list is implemented as a double queue. See http://www.lua.org/pil/11.4.html

### Returns:

1. list The created list.

[]()**popleft (list)**

Remove and return and element from the left (or front) of the list.

### Parameters:

- list The list object.

### Returns:

1. value The removed object from the left (or front) of the list.

[]()**size (list)**

Get the size of a List object.

### Parameters:

- list The list object.

### Returns:

1. size The total number of elements in the list.

### See also:

- [List.new](#module-list#new)

## []()Doubly LinkedList

[]()**LinkedList.isEmpty (list)**

Check if the specified linked list is empty.

### Parameters:

- list The linked list object.

### Returns:

1. boolean Returns true if there are no elements in the linked list. False, otherwise.

[]()**LinkedList.nodes (list)**

Stateless iterator for a linked list.

### Parameters:

- list The linked list object.

### Returns:

1. iterator The iterator function.
2. list The invariant state.
3. control The control variable.

### Usage:

- ```
  for node in LinkedList.nodes(list) do
    something
  end
  ```

[]()**LinkedList.size (list)**

Get the total number of elements in a linked list.

### Parameters:

- list The linked list object.

### Returns:

1. size The total number of elements in the linked list.

---

# Module `Log`

Enhanced logging functions ONLY for use in Lua state functions and helpers.

Log.info(), Log.console(), Log.warn(), and Log.error() automatically fill in mapped diagnostic content (MDC) tags from the calling context.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[console (message)](#console) Log a message to the FlexTerm output console.

## []()Functions

[]()**console (message)**

Log a message to the FlexTerm output console. This message will also appear in the output log.

### Parameters:

- message The message to log to the output console.

---

# Module `MoveHelper`

Helper functions for horizontal transport navigation.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[navigateToParkingSpot (ht, parkingSpot, arrivedSignal, signalData)](#navigateToParkingSpot) Generic helper function to navigate to a destination parking spot.

## []()Functions

[]()**navigateToParkingSpot (ht, parkingSpot, arrivedSignal, signalData)**

Generic helper function to navigate to a destination parking spot.

### Parameters:

- ht The horizontal transport object.
- parkingSpot The parking spot index to navigate to.
- arrivedSignal Optional. The signal to emit at the completion of the navigation. If not specified, then the default signal will be "arrived\_at\_destination" signal.
- signalData Optional. The data associated with the signal emitted.

---

# Module `ParkingHelper`

Helper functions for creating various categories of parking spots.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[createParkingLot (existingIndex, offset, rotation, length, roadName)](#createParkingLot) Create a single parking spot with collision detection off.

## []()Functions

[]()**createParkingLot (existingIndex, offset, rotation, length, roadName)**

Create a single parking spot with collision detection off. Makes a new perpendicular multilane to the supplied index one.

### Parameters:

- existingIndex The multilane index of the multilane to connect the created perpendicular multilane to.
- offset The x-offset on the existing multilane to create the perpendicular multilane.
- rotation The rotation offset (between -180 and +180) of the perpendicular multilane relative to the existing multilane.
- length The length of the perpendicular multilane.
- roadName The display name of the perpendicular multilane.

### Returns:

1. parkingSpot The parking spot index of the parking spot.
2. perpLaneObj The perpendicular multilane object.

---

# Module `SimTOSDispatcherHelper`

Helper functions for SimTOS dispatcher functions.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[addToNotifyDispatchersMap (dispatchersMap, dispatcher)](#addToNotifyDispatchersMap) Store unique dispatchers in the specified dispatcher map. [assignYardCraneToJob (job)](#assignYardCraneToJob) Helper function to assign a yard crane to a job. [dispatchHorizontalTransportJob (che, job)](#dispatchHorizontalTransportJob) Dispatch the job to the specified CHE. [getHorizontalTransportDispatcher (che)](#getHorizontalTransportDispatcher) Get the horizontal transport dispatcher for the specified CHE. [notifyDispatchersInDispatchersMap (dispatcherMap)](#notifyDispatchersInDispatchersMap) Notify all unique dispatchers in the specified dispatcher map. [transportDispatch (idleWorkers, pendingJobs, serviceCraneScoringFunction, jobScoringFunction, transportScoringFunction)](#transportDispatch) Helper function to dispatch a job to a horizontal transport. [updateQuayCraneStatusDispatched (job, quayCrane)](#updateQuayCraneStatusDispatched) Updates the vessel job with the specified quay crane.

## []()Functions

[]()**addToNotifyDispatchersMap (dispatchersMap, dispatcher)**

Store unique dispatchers in the specified dispatcher map. This helper function is used to aggregate unique dispatchers and to request dispatch after all jobs are added (notify once instead of multiple times).

### Parameters:

- dispatchersMap The temporary map that will hold all of the unique dispatchers.
- dispatcher The dispatcher to add to the temporary map.

### See also:

- [SimTOSDispatcherHelper.notifyDispatchersInDispatchersMap](#module-simtosdispatcherhelper#notifyDispatchersInDispatchersMap)

[]()**assignYardCraneToJob (job)**

Helper function to assign a yard crane to a job. After yard crane is selected, this function will set the yard crane for the job.

### Parameters:

- job The job to assign a yard crane to.

### Returns:

1. yardCrane The yard that was assigned to the job.

### See also:

- [SimTOSYardCraneHelper.getYardCraneForJob](#module-simtosyardcranehelper#getYardCraneForJob)

[]()**dispatchHorizontalTransportJob (che, job)**

Dispatch the job to the specified CHE. This will add the job to the "jobs" list of the CHE. Additionally, this will signal the check to check for work by signaling "do\_next\_job" to the CHE at the current simulation time.

### Parameters:

- che The CHE to dispatch the job to.
- job The job to dispatch to the CHE. TODO: here, if load pm already mounted, then skip pickup

[]()**getHorizontalTransportDispatcher (che)**

Get the horizontal transport dispatcher for the specified CHE. This function will retrieve a reference to the horizontal transport dispatcher specified by the che's "horizontalTransportDispatcherID" property. If this property is not set, then a reference to the default global horizontal transport dispatcher will be returned.

### Parameters:

- che The che object to retrieve the dispatcher.

### Returns:

1. horizontalTransportDispatcher The horizontal transport dispatcher assigned to the che. Default global horizontal transport dispatcher if "horizontalTransportDispatcherID" property is not specified.
2. dispatcherType The type name of the horizontal transport dispatcher.

[]()**notifyDispatchersInDispatchersMap (dispatcherMap)**

Notify all unique dispatchers in the specified dispatcher map. This will notify all of the dispatchers in the dispatcher map by signaling "dispatch\_requested" to each dispatcher at the current simulation time.

### Parameters:

- dispatcherMap The temporary map holding all of the unique dispatcher to notify.

### See also:

- [SimTOSDispatcherHelper.addToNotifyDispatchersMap](#module-simtosdispatcherhelper#addToNotifyDispatchersMap)

[]()**transportDispatch (idleWorkers, pendingJobs, serviceCraneScoringFunction, jobScoringFunction, transportScoringFunction)**

Helper function to dispatch a job to a horizontal transport.

### Parameters:

- idleWorkers The list of idle workers.
- pendingJobs The list of pending jobs.
- serviceCraneScoringFunction The scoring function to prioritize the search order of service crane's jobs to search.
- jobScoringFunction The scoring function to score each job of a service crane.
- transportScoringFunction The scoring function to score each idle transport for a selected job.

### Returns:

1. boolean Returns true if a job was matched to a transport. False, otherwise.

[]()**updateQuayCraneStatusDispatched (job, quayCrane)**

Updates the vessel job with the specified quay crane. This will set the job status to "Dispatched" and set the quay crane of the job to the ID of the specified quay crane.

### Parameters:

- job The vessel job to assign the quay crane to.
- quayCrane The quay crane to assign to the vessel job.

### Returns:

1. isPairLift Returns true if the vessel job is a twin or tandem lift.
2. job1 The first job of a pair lift (or the only job if single lift).
3. job2 The second job of a pair lift. This will be nil if the job is single lift.

---

# Module `SimTOSDispatchFunctions`

SimTOS dispatch state functions.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[DispatchFunctions.dispatchHorizontalTransport ()](#DispatchFunctions.dispatchHorizontalTransport) Horizontal Transport Dispatcher. [DispatchFunctions.vesselJobDispatch ()](#DispatchFunctions.vesselJobDispatch) Vessel Job Dispatcher. [DispatchFunctions.yardCraneDispatch ()](#DispatchFunctions.yardCraneDispatch) Yard Crane Dispatcher Assigns a yard job to a yard crane.

## []()Functions

[]()**DispatchFunctions.dispatchHorizontalTransport ()**

Horizontal Transport Dispatcher. Assigns pickup/dropoff tasks from/to vessel for horizontal transport load/discharge vessel jobs. The actual vessel job will be added to the quay crane when the horizontal transport navigates to the quay crane. The amount of idle PMs is the same as the amount of pending vessel jobs.

### See also:

- [DispatchFunctions.vesselJobDispatch](#module-simtosdispatchfunctions#DispatchFunctions.vesselJobDispatch)

[]()**DispatchFunctions.vesselJobDispatch ()**

Vessel Job Dispatcher. Randomly select a vessel job from all working vessels and request a horizontal transport from the quay crane's dispatcher. If none, use global horizontal transport dispatcher. This function removes the first job in a randomly selected quay crane's job list and adds it to the horizontal transport dispatcher pending jobs queue to be dispatched. Only dispatches as many jobs as there are idle PMs.

[]()**DispatchFunctions.yardCraneDispatch ()**

Yard Crane Dispatcher Assigns a yard job to a yard crane. The workers are the yard cranes on the block's extent.

---

# Module `SimTOSDualTrolleyQuayCraneHelper`

Helper functions for dual trolley quay crane state functions.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[getReadyJobs (trolley, jobs)](#getReadyJobs) Helper function to get the trolley jobs that are ready to be worked. [pickNextJob (trolley)](#pickNextJob) Pick the next job from the jobs list.

## []()Functions

[]()**getReadyJobs (trolley, jobs)**

Helper function to get the trolley jobs that are ready to be worked. This will split the ready jobs into 2 lists: ready (non-platform) jobs, and platform ready jobs. Non-platform ready jobs are jobs that will be moved into the platform (i.e., container is currently outside of the platform). For primary trolley, this means that the job is a discharge operation and the container is currently in the vessel (i.e., beginning of a discharge operation); for secondary trolley, this means that the job is a load operation and the container is currently on an arrived prime mover or is currently on the wharf (i.e., beginning of a load operation). If the primary trolley is performing a tandem discharge, it will reserve the platform. Platform ready jobs are jobs that will be moved out of the platform (i.e., container is currently on the platform). For primary trolley, this means the job is a load operation; for secondary trolley, this means the job is a discharge operation (additionally, either a prime mover must be arrived under the quay or wharf slot is available).

### Parameters:

- trolley The trolley object.
- jobs The current jobs list of the trolley object.

### Returns:

1. readyJobs The list of non-platform ready jobs. If no ready jobs, returns an empty list.
2. platformReadyJobs The list of platform ready jobs. If no ready jobs, returns an empty list.

### See also:

- [DualTrolleyQuayCraneHelper.reservePlatform](#module-dualtrolleyquaycranehelper#reservePlatform)

[]()**pickNextJob (trolley)**

Pick the next job from the jobs list. The selected job must be removed from the jobs list and returned.

### Parameters:

- trolley The trolley object.

### Returns:

1. selectedJob The selected job to do next. Return nil if no job selected.

### See also:

- [JobList.removeJob](#module-joblist#removeJob)

---

# Module `SimTOSGantryCraneHelper`

Helper functions for gantry crane state functions.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[findNextPriorityLoadJob (gantryCrane, tandemReadyJobs, twinReadyJobs, readyJobs)](#findNextPriorityLoadJob) Find the next priority load job. [findPriorityLoadJobs (gantryCrane, readyJobs)](#findPriorityLoadJobs) Find any load jobs that are within a distance threshold from the next job in the crane's sequence. [getAlternatePreferredLoadDischargeOperation ()](#getAlternatePreferredLoadDischargeOperation) Getter for the flag to alternate bewteen load and discharge operations for preferred ready jobs. [getEndFeedBayOccupancyThreshold ()](#getEndFeedBayOccupancyThreshold) Getter for the end feed bay occupancy threshold to prefer switching bewteen load and discharge operations for preferred ready jobs. [getLoadSequenceDistanceThreshold ()](#getLoadSequenceDistanceThreshold) Getter for the job distance threshold value to prioritize load jobs. [getReadyJobs (gantryCrane, jobs)](#getReadyJobs) Helper function to get the gantry crane jobs that are ready to be worked. [pickNextJob (gantryCrane)](#pickNextJob) Pick the next job from the jobs list. [selectPreferredReadyJob (gantryCrane, readyJobs)](#selectPreferredReadyJob) Select from the list of ready jobs based on the next preferred operation type. [setAlternatePreferredLoadDischargeOperation (flag)](#setAlternatePreferredLoadDischargeOperation) Set the flag to alternate bewteen load and discharge operations for preferred ready jobs. [setEndFeedBayOccupancyThreshold (value)](#setEndFeedBayOccupancyThreshold) Set the end feed bay occupancy threshold to prefer switching bewteen load and discharge operations for preferred ready jobs. [setLoadSequenceDistanceThreshold (value)](#setLoadSequenceDistanceThreshold) Set the job distance threshold value to prioritize load jobs. [sortByNextJobInSequence (job1, job2)](#sortByNextJobInSequence) Sorting function for load jobs.

## []()Functions

[]()**findNextPriorityLoadJob (gantryCrane, tandemReadyJobs, twinReadyJobs, readyJobs)**

Find the next priority load job.

### Parameters:

- gantryCrane The gantry crane object.
- tandemReadyJobs The list of tandem lift ready jobs.
- twinReadyJobs The list of twin lift ready jobs.
- readyJobs The list of single lift ready jobs.

### Returns:

1. selectedJob The load job to do next. Return nil if no job selected.

[]()**findPriorityLoadJobs (gantryCrane, readyJobs)**

Find any load jobs that are within a distance threshold from the next job in the crane's sequence. If the distance threshold value is not set, then this will return an empty list (no jobs have priority).

### Parameters:

- gantryCrane The gantry crane object.
- readyJobs The list of ready jobs.

### Returns:

1. priorityLoadJobs The list of load jobs that are within a distance threshold from the next job in the crane's sequence. If no jobs have priority, then this will return an empty list.

### See also:

- [SimTOSGantryCraneHelper.setLoadSequenceDistanceThreshold](#module-simtosgantrycranehelper#setLoadSequenceDistanceThreshold)

[]()**getAlternatePreferredLoadDischargeOperation ()**

Getter for the flag to alternate bewteen load and discharge operations for preferred ready jobs.

### Returns:

1. boolean Returns true if alternating bewteen load and discharge operations for preferred ready jobs.

[]()**getEndFeedBayOccupancyThreshold ()**

Getter for the end feed bay occupancy threshold to prefer switching bewteen load and discharge operations for preferred ready jobs.

### Returns:

1. value The occupancy threshold percentage (in decimal format).

[]()**getLoadSequenceDistanceThreshold ()**

Getter for the job distance threshold value to prioritize load jobs.

### Returns:

1. value The job distance threshold value to prioritize load jobs.

[]()**getReadyJobs (gantryCrane, jobs)**

Helper function to get the gantry crane jobs that are ready to be worked. This will split the ready jobs into 3 lists: twin ready jobs, tandem ready jobs, and all other ready jobs (single lift). Twin and tandem ready jobs are jobs that have started operations for one job in the pair. For twin lift operations, a twin ready job depends on the mounting status of the other pair container. If twin discharge, this means that one container is already in the yard. If twin load, this means that one container is arleady mounted. For tandem lift operations, this means that the other container is already assigned to another prime mover. For single lift operations, this means that the prime mover has arrived at the yard block.

### Parameters:

- gantryCrane The gantry crane object.
- jobs The current jobs list of the gantry crane object.

### Returns:

1. readyJobs The list of single lift ready jobs. If no ready jobs, returns an empty list.
2. twinReadyJobs The list of twin lift ready jobs. If no ready jobs, returns an empty list.
3. tandemReadyJobs The list of tandem lift ready jobs. If no ready jobs, returns an empty list.

[]()**pickNextJob (gantryCrane)**

Pick the next job from the jobs list. The selected job must be removed from the jobs list and returned.

### Parameters:

- gantryCrane The gantry crane object.

### Returns:

1. selectedJob The selected job to do next. Return nil if no job selected.

### See also:

- [JobList.removeJob](#module-joblist#removeJob)

[]()**selectPreferredReadyJob (gantryCrane, readyJobs)**

Select from the list of ready jobs based on the next preferred operation type. For end loaded blocks, this depends on an occupancy threshold value. For all other blocks, this depends on the gantry crane's last completed job.

### Parameters:

- gantryCrane The gantry crane object.
- readyJobs The list of ready jobs.

### Returns:

1. selectedJob The selected job to do next. By default, will return the first ready job in the list. Return nil if no job selected.

### See also:

- [SimTOSGantryCraneHelper.setAlternatePreferredLoadDischargeOperation](#module-simtosgantrycranehelper#setAlternatePreferredLoadDischargeOperation)
- [SimTOSGantryCraneHelper.setEndFeedBayOccupancyThreshold](#module-simtosgantrycranehelper#setEndFeedBayOccupancyThreshold)

[]()**setAlternatePreferredLoadDischargeOperation (flag)**

Set the flag to alternate bewteen load and discharge operations for preferred ready jobs.

### Parameters:

- flag Set to true to alternate between load and discharge jobs when selecting the next job to do.

[]()**setEndFeedBayOccupancyThreshold (value)**

Set the end feed bay occupancy threshold to prefer switching bewteen load and discharge operations for preferred ready jobs. The current discharge/load occupancy is determined by the number of end feed bay slots occupied by discharge/load operation(s) divided by the total number of end feed bay slots. For horizontal transports, if there are too many load transports in the end feed bay (load occupancy exceeds threshold value), then prefer load jobs. If there are too many discharge transports in the end feed bay (discharge occupancy exceeds threshold value), then prefer discharge jobs. For straddle transports, if there are too many load/discharge containers in the end feed bay (occupancy exceeds threshold value), then prefer discharge jobs.

### Parameters:

- value The occupancy threshold percentage (in decimal format). The default value is set to 0.5.

[]()**setLoadSequenceDistanceThreshold (value)**

Set the job distance threshold value to prioritize load jobs. The job distance value is the difference between the gantry crane's next job's index and another job's index. If the job distance value is less than or equal to the threshold value, then it will be prioritized over other load jobs.

### Parameters:

- value The job distance threshold value for load jobs.

[]()**sortByNextJobInSequence (job1, job2)**

Sorting function for load jobs. Sort the load jobs by the next job in the quay crane's job sequence.

### Parameters:

- job1 The first job.
- job2 The second job.

### Returns:

1. boolean Return true if job1 should precede job2 in the sorted job list. False, otherwise.

---

# Module `SimTOSJobHelper`

Helper functions for job data.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[getPMArrived (job)](#getPMArrived) Get the job property for PM arrival. [getPrimeMover (job)](#getPrimeMover) Get the prime mover associated with the job. [setPMArrived (job, arrived)](#setPMArrived) Set the job property for PM arrival.

## []()Functions

[]()**getPMArrived (job)**

Get the job property for PM arrival.

### Parameters:

- job The job data.

### Returns:

1. boolean True if the PM arrival property is set to true. False, otherwise.

[]()**getPrimeMover (job)**

Get the prime mover associated with the job.

### Parameters:

- job The job data.

### Returns:

1. pmID The object ID of the prime mover associated with the job. If no prime mover associated, then returns nil.
2. pm The prime mover object. If no prime mover associated, then returns nil.

[]()**setPMArrived (job, arrived)**

Set the job property for PM arrival.

### Parameters:

- job The job data.
- arrived Set to true to mark the PM as arrived.

---

# Module `SimTOSQuayCraneHelper`

Helper functions for single trolley quay crane state functions.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[checkForDualTrolleyAndForwardJobs (quayCrane, jobs)](#checkForDualTrolleyAndForwardJobs) Helper function to forward dual trolley quay crane jobs to the appropriate trolley. [getReadyJobsForStraddle (quayCrane, jobs)](#getReadyJobsForStraddle) Helper function to get the jobs that are ready to be worked by straddle carriers. [pickNextJob (quayCrane)](#pickNextJob) Pick the next job from the jobs list.

## []()Functions

[]()**checkForDualTrolleyAndForwardJobs (quayCrane, jobs)**

Helper function to forward dual trolley quay crane jobs to the appropriate trolley. Discharge jobs are initially dispatched to the primary trolley. Load jobs are initially dispatched to the secondary trolley.

### Parameters:

- quayCrane The quay crane object.
- jobs The current jobs list of the quay crane object.

[]()**getReadyJobsForStraddle (quayCrane, jobs)**

Helper function to get the jobs that are ready to be worked by straddle carriers. Discharge containers are ready if there are available slot(s) on the wharf. Load containers are ready if they are currently on the wharf.

### Parameters:

- quayCrane The quay crane object.
- jobs The current jobs list of the quay crane object.

### Returns:

1. readyJobs The list of wharf jobs that are ready to be worked.

[]()**pickNextJob (quayCrane)**

Pick the next job from the jobs list. The selected job must be removed from the jobs list and returned.

### Parameters:

- quayCrane The quay crane object.

### Returns:

1. selectedJob The selected job to do next. Return nil if no job selected.

### See also:

- [JobList.removeJob](#module-joblist#removeJob)

---

# Module `SimTOSScenarioHelper`

Helper functions for the scenario tables used for simulation scenario generation.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[getHorizontalTransportDispatcher ()](#getHorizontalTransportDispatcher) Get the default global horizontal transport dispatcher. [getServiceCall (serviceID, callNumber)](#getServiceCall) get the service call table for the corresponding service ID and call number [getYardCraneDispatcher ()](#getYardCraneDispatcher) Get the default global yard crane dispatcher.

## []()Functions

[]()**getHorizontalTransportDispatcher ()**

Get the default global horizontal transport dispatcher. Returns a reference to the dispatcher specified in the terminal data property "horizontalTransportDispatcherID." The default dispatch function is "DispatchFunctions.dispatchHorizontalTransport."

### Returns:

1. dispatcher The default global horizontal transport dispatcher.

[]()**getServiceCall (serviceID, callNumber)**

get the service call table for the corresponding service ID and call number

### Parameters:

- serviceID
- callNumber

[]()**getYardCraneDispatcher ()**

Get the default global yard crane dispatcher. Returns a reference to the dispatcher specified in the terminal data property "yardCraneDispatcherID." The default dispatch function is "DispatchFunctions.yardCraneDispatch."

### Returns:

1. dispatcher The default global yard crane dispatcher.

---

# Module `SimTOSScoringFunctions`

SimTOS scoring functions.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [BayScoringFunctions](#BayScoringFunctions)

[BayScoringFunctions.bayOrder (container, bayNumber, bay)](#BayScoringFunctions.bayOrder) Score bays in order of the bay number.

## [CellScoringFunctions](#CellScoringFunctions)

[CellScoringFunctions.cellOrder (container, cellNumber, cell)](#CellScoringFunctions.cellOrder) Score cells in order of the cell number. [CellScoringFunctions.inverseCellOrder (container, cellNumber, cell)](#CellScoringFunctions.inverseCellOrder) Score cells in inverse order of the cell number.

## [VesselScoringFunctions](#VesselScoringFunctions)

[VesselScoringFunctions.defaultVesselScoringFunction (container, holders, holderID, bayNumber, cellNumber, tierNumber, holderModifier, job)](#VesselScoringFunctions.defaultVesselScoringFunction) The default vessel planning scoring function.

## [ServiceCraneScoringFunctions](#ServiceCraneScoringFunctions)

[ServiceCraneScoringFunctions.balancedTransports (serviceCrane)](#ServiceCraneScoringFunctions.balancedTransports) Decide the order in which service cranes are evaluated when dispatching horizontal transports to them. [ServiceCraneScoringFunctions.randomCrane (serviceCrane)](#ServiceCraneScoringFunctions.randomCrane) Decide the order in which service cranes are evaluated when dispatching horizontal transport to them.

## [JobScoringFunctions](#JobScoringFunctions)

[JobScoringFunctions.anyJob (job)](#JobScoringFunctions.anyJob) Select any job. [JobScoringFunctions.followVesselJobSequenceIndex (job)](#JobScoringFunctions.followVesselJobSequenceIndex) Select the next job following the vessel job sequence index. [JobScoringFunctions.randomJob (job)](#JobScoringFunctions.randomJob) Select a random job.

## [TransportScoringFunctions](#TransportScoringFunctions)

[TransportScoringFunctions.closestToJob (transport, job, index)](#TransportScoringFunctions.closestToJob) Select the next transport that is closest to the service job.

## [YardCraneScoringFunctions](#YardCraneScoringFunctions)

[YardCraneScoringFunctions.defaultYardCraneScoringFunction (job, container, yardCrane, yardCranesInRange)](#YardCraneScoringFunctions.defaultYardCraneScoringFunction) Default yard crane scoring function.

## []()BayScoringFunctions

[]()**BayScoringFunctions.bayOrder (container, bayNumber, bay)**

Score bays in order of the bay number.

### Parameters:

- container The container data.
- bayNumber The bay number.
- bay The bay holder.

### Returns:

1. score The score for this container and bay.

## []()CellScoringFunctions

[]()**CellScoringFunctions.cellOrder (container, cellNumber, cell)**

Score cells in order of the cell number.

### Parameters:

- container The container data.
- cellNumber The cell number.
- cell The cell holder.

### Returns:

1. score The score for this container and cell.

[]()**CellScoringFunctions.inverseCellOrder (container, cellNumber, cell)**

Score cells in inverse order of the cell number.

### Parameters:

- container The container data.
- cellNumber The cell number.
- cell The cell holder.

### Returns:

1. score The score for this container and cell.

## []()VesselScoringFunctions

[]()**VesselScoringFunctions.defaultVesselScoringFunction (container, holders, holderID, bayNumber, cellNumber, tierNumber, holderModifier, job)**

The default vessel planning scoring function.

### Parameters:

- container The container data.
- holders The hatch holders of the vessel.
- holderID The hatch holder ID.
- bayNumber The bay number.
- cellNumber The cell number.
- tierNumber The tier number.
- holderModifier The modifer for the hatch holder. It can either be "aboveDeck" or "belowDeck."
- job The vessel job data.

### Returns:

1. score The score for this container and slot. Return -ScoringFunctions.HugeNumber to discard this slot.
2. skipFurtherTiers Return true to skip evaluation of remaining tiers for the current bay and cell. This will proceed to evaluate the next cell.
3. skipFurtherCells Return true to skip evaluation of remaining cells for the current bay. This will proceed to evaluate the next bay.
4. skipFurtherBays Return true to skip evaluation of remaining bays. This will proceed to end scoring and return the current best score and slot.
5. skipFurtherHolders Return true to skip evaluation of remaining holders. If the current best score is greater than -ScoringFunctions.HugeNumber, then this will plan the container to the current best slot.
6. reasonForNotConsidering A string describing the reason for the returned score. Typically used for logging for discarded slots, but may be used for debugging available slots during planning.

## []()ServiceCraneScoringFunctions

[]()**ServiceCraneScoringFunctions.balancedTransports (serviceCrane)**

Decide the order in which service cranes are evaluated when dispatching horizontal transports to them.

### Parameters:

- serviceCrane The service crane (e.g., quay crane or rail crane).

### Returns:

1. score The score assigned to the service crane.

[]()**ServiceCraneScoringFunctions.randomCrane (serviceCrane)**

Decide the order in which service cranes are evaluated when dispatching horizontal transport to them.

### Parameters:

- serviceCrane

## []()JobScoringFunctions

[]()**JobScoringFunctions.anyJob (job)**

Select any job.

### Parameters:

- job

[]()**JobScoringFunctions.followVesselJobSequenceIndex (job)**

Select the next job following the vessel job sequence index.

### Parameters:

- job The vessel job.

### Returns:

1. score The score assigned to the vessel job.

[]()**JobScoringFunctions.randomJob (job)**

Select a random job.

### Parameters:

- job

## []()TransportScoringFunctions

[]()**TransportScoringFunctions.closestToJob (transport, job, index)**

Select the next transport that is closest to the service job.

### Parameters:

- transport The horizontal transport.
- job The service job.
- index The index of the horizontal transport in the idle transports list.

### Returns:

1. score The score assigned to the transport/job pair.

## []()YardCraneScoringFunctions

[]()**YardCraneScoringFunctions.defaultYardCraneScoringFunction (job, container, yardCrane, yardCranesInRange)**

Default yard crane scoring function. Assign a score to the yard crane/job pair.

### Parameters:

- job The yard job.
- container The container object.
- yardCrane The yard crane object. This yard crane is part of the yardCranesInRange list.
- yardCranesInRange The list of yard crane objects assigned to the yard block of the job.

### Returns:

1. score The score assigned to the yard crane/job pair.

---

# Module `SimTOSSingleStackPrimeMoverHelper`

Helper functions for single stack prime mover state functions.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[pickNextJob (pm)](#pickNextJob) Pick the next job from the jobs list.

## []()Functions

[]()**pickNextJob (pm)**

Pick the next job from the jobs list. The selected job must be removed from the jobs list and returned.

### Parameters:

- pm The prime mover object.

### Returns:

1. selectedJob The selected job to do next. Return nil if no job selected.

### See also:

- [JobList.removeJob](#module-joblist#removeJob)

---

# Module `SimTOSVesselHelper`

Helper functions for vessel data.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[getWorkingVesselsCraneList (vessels)](#getWorkingVesselsCraneList) Get a list of working quay cranes for each vessel.

## []()Functions

[]()**getWorkingVesselsCraneList (vessels)**

Get a list of working quay cranes for each vessel.

### Parameters:

- vessels The list of vessels (objects) to search through.

### Returns:

1. workingVessels The list of active working vessels (as object references).
2. craneList A map of vessel index (which is an index into the workingVessels list) to list of working quay cranes (as object IDs).
3. totalPendingJobs The total number of jobs in all the working job lists of all working quay cranes.

---

# Module `SimTOSYardBlockHelper`

Helper functions for yard blocks.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[getAssignedCranes (yardBlockID)](#getAssignedCranes) Get the yard block's assigned yard cranes.

## []()Functions

[]()**getAssignedCranes (yardBlockID)**

Get the yard block's assigned yard cranes. These are the yard cranes assigned to the yard block via the yard crane assignments.

### Parameters:

- yardBlockID The ID of the yard block.

### Returns:

1. yardCraneIDs The yard crane IDs assigned to the yard block.

---

# Module `SimTOSYardCraneHelper`

Helper functions for yard cranes.

### Info:

- **Copyright**: Copyright (c) Moffatt &amp; Nichol. All rights reserved.
- **Author**: FlexTerm

## [Functions](#Functions)

[\_getYardCraneForJob (job, scoringFunction)](#_getYardCraneForJob) Base function to get yard cranes for job. [getYardCraneForJob (job, scoringFunction)](#getYardCraneForJob) Overrideable function to get yard cranes for job. [getYardCranesInRangeOfBlock (yardBlockID)](#getYardCranesInRangeOfBlock) Get the yard cranes assigned to the yard block.

## []()Functions

[]()**\_getYardCraneForJob (job, scoringFunction)**

Base function to get yard cranes for job. This will pass the list of yard cranes assigned to the yard block of the job to the yard crane scoring function.

### Parameters:

- job The yard job.
- scoringFunction The yard crane scoring function to call for each yard crane/job combination. If not specified, defaults to YardCraneScoringFunctions.defaultYardCraneScoringFunction.

### Returns:

1. yardCrane The yard crane selected for the job.

### See also:

- [SimTOSYardCraneHelper.getYardCranesInRangeOfBlock](#module-simtosyardcranehelper#getYardCranesInRangeOfBlock)

[]()**getYardCraneForJob (job, scoringFunction)**

Overrideable function to get yard cranes for job.

### Parameters:

- job The yard job.
- scoringFunction The yard crane scoring function to call for each yard crane/job combination.

### Returns:

1. yardCrane The yard crane selected for the job.

[]()**getYardCranesInRangeOfBlock (yardBlockID)**

Get the yard cranes assigned to the yard block.

### Parameters:

- yardBlockID The ID of the yard block.

### Returns:

1. yardCranes The yard cranes (as objects) assigned to the yard block, sorted by their center to center distance to the yard block.

### See also:

- [SimTOSYardBlockHelper.getAssignedCranes](#module-simtosyardblockhelper#getAssignedCranes)
