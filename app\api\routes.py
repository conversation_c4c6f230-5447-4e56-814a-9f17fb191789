"""API routes for FlexTerm Q&A service."""

import logging
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse

from app.models.schemas import (
    QuestionRequest, 
    QuestionResponse, 
    ErrorResponse, 
    HealthResponse
)
from app.services.qa_service import get_qa_service
from app.services.vector_store import get_vector_store
from app.core.embeddings import get_embedding_service
from app import __version__

logger = logging.getLogger(__name__)

# Create API router
router = APIRouter()


@router.post("/ask", response_model=QuestionResponse)
async def ask_question(
    request: QuestionRequest,
    qa_service = Depends(get_qa_service)
) -> QuestionResponse:
    """
    Answer a question about FlexTerm using RAG.
    
    This endpoint processes user questions about FlexTerm simulation and emulation,
    retrieves relevant documentation context, and generates both verbose and concise responses.
    """
    try:
        logger.info(f"Received question: {request.question[:100]}...")
        
        # Check if service is ready
        if not qa_service.is_ready():
            raise HTTPException(
                status_code=503,
                detail="Q&A service is not ready. Please ensure the vector database is indexed."
            )
        
        # Process the question
        response = await qa_service.answer_question(request.question)
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in ask_question: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/health", response_model=HealthResponse)
async def health_check(
    vector_store = Depends(get_vector_store),
    embedding_service = Depends(get_embedding_service)
) -> HealthResponse:
    """
    Health check endpoint.
    
    Returns the status of various service components including:
    - Overall service status
    - Application version
    - Ollama availability
    - Vector database readiness
    """
    try:
        # Check vector database
        vector_db_ready = vector_store.is_ready() and vector_store.get_document_count() > 0
        
        # Check Ollama availability
        ollama_available = embedding_service.ollama_available
        
        # Determine overall status
        if vector_db_ready and (ollama_available or embedding_service.openai_client):
            status = "healthy"
        elif vector_db_ready:
            status = "degraded"  # Vector DB ready but embedding issues
        else:
            status = "unhealthy"
        
        return HealthResponse(
            status=status,
            version=__version__,
            ollama_available=ollama_available,
            vector_db_ready=vector_db_ready
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return HealthResponse(
            status="error",
            version=__version__,
            ollama_available=False,
            vector_db_ready=False
        )


@router.get("/stats")
async def get_stats(vector_store = Depends(get_vector_store)):
    """
    Get statistics about the knowledge base.
    
    Returns information about indexed documents and system status.
    """
    try:
        document_count = vector_store.get_document_count()
        embedding_service = get_embedding_service()
        
        stats = {
            "documents_indexed": document_count,
            "embedding_dimension": embedding_service.get_embedding_dimension(),
            "ollama_available": embedding_service.ollama_available,
            "embedding_model": embedding_service.embedding_service.embedding_model if embedding_service.ollama_available else "openai-fallback"
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve statistics")


# Error handlers
@router.exception_handler(HTTPException)
async def http_exception_handler(request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            status="error",
            message=exc.detail,
            detail=f"HTTP {exc.status_code}"
        ).dict()
    )


@router.exception_handler(Exception)
async def general_exception_handler(request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            status="error",
            message="Internal server error",
            detail=str(exc)
        ).dict()
    )
